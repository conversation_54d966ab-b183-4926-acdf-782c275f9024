-- Create the spotify_users table
CREATE TABLE IF NOT EXISTS spotify_users (
  id SERIAL PRIMARY KEY,
  spotify_id TEXT UNIQUE NOT NULL,
  display_name TEXT,
  email TEXT,
  profile_image TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on spotify_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_spotify_users_spotify_id ON spotify_users(spotify_id);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at column
DROP TRIGGER IF EXISTS update_spotify_users_updated_at ON spotify_users;
CREATE TRIGGER update_spotify_users_updated_at
BEFORE UPDATE ON spotify_users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
