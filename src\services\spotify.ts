// Types for Spotify API responses
export interface SpotifyTrack {
  id: string;
  name: string;
  album: {
    id: string;
    name: string;
    images: { url: string; height: number; width: number }[];
  };
  artists: { id: string; name: string }[];
  duration_ms: number;
  popularity: number;
  preview_url: string | null;
  external_urls: { spotify: string };
}

export interface SpotifyArtist {
  id: string;
  name: string;
  images: { url: string; height: number; width: number }[];
  genres: string[];
  popularity: number;
  external_urls: { spotify: string };
}

export interface SpotifyPlayHistory {
  track: SpotifyTrack;
  played_at: string;
  context: {
    type: string;
    uri: string;
    external_urls: { spotify: string };
  } | null;
}

export interface SpotifyPaging<T> {
  href: string;
  items: T[];
  limit: number;
  next: string | null;
  offset: number;
  previous: string | null;
  total: number;
}

export interface SpotifyPlaylist {
  id: string;
  name: string;
  external_url: string;
  track_count: number;
  time_range: TimeRange;
  created_at?: string;
  last_updated_at?: string;
}

export type TimeRange = 'short_term' | 'medium_term' | 'long_term';

// Service class for Spotify API
export class SpotifyService {
  private accessToken: string | null = null;

  constructor() {
    // Try to get the access token from localStorage
    const storedTokens = localStorage.getItem('spotify_tokens');
    if (storedTokens) {
      try {
        const parsedTokens = JSON.parse(storedTokens);
        this.accessToken = parsedTokens.accessToken;
      } catch (error) {
        console.error('Error parsing stored tokens:', error);
      }
    }
  }

  // Set the access token
  setAccessToken(token: string) {
    this.accessToken = token;
  }

  // Get the user's top tracks
  async getTopTracks(timeRange: TimeRange = 'medium_term', limit: number = 20): Promise<SpotifyPaging<SpotifyTrack>> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    const response = await fetch(`/.netlify/functions/top-tracks?access_token=${this.accessToken}&time_range=${timeRange}&limit=${limit}`);

    if (!response.ok) {
      throw new Error('Failed to fetch top tracks');
    }

    return await response.json();
  }

  // Get the user's top artists
  async getTopArtists(timeRange: TimeRange = 'medium_term', limit: number = 20): Promise<SpotifyPaging<SpotifyArtist>> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    const response = await fetch(`/.netlify/functions/top-artists?access_token=${this.accessToken}&time_range=${timeRange}&limit=${limit}`);

    if (!response.ok) {
      throw new Error('Failed to fetch top artists');
    }

    return await response.json();
  }

  // Get the user's recently played tracks
  async getRecentlyPlayed(limit: number = 20): Promise<SpotifyPaging<SpotifyPlayHistory>> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    const response = await fetch(`/.netlify/functions/recently-played?access_token=${this.accessToken}&limit=${limit}`);

    if (!response.ok) {
      throw new Error('Failed to fetch recently played tracks');
    }

    return await response.json();
  }

  // Get the user's top tracks by a specific artist
  async getArtistTopTracks(artistId: string, limit: number = 5, timeRange: TimeRange = 'medium_term'): Promise<SpotifyPaging<SpotifyTrack>> {
    // Create a fallback empty data structure
    const emptyData: SpotifyPaging<SpotifyTrack> = {
      items: [],
      total: 0,
      limit: 0,
      href: '',
      next: null,
      offset: 0,
      previous: null
    };

    if (!this.accessToken) {
      console.error('No access token available for getArtistTopTracks');
      return emptyData;
    }

    if (!artistId) {
      console.error('No artist ID provided for getArtistTopTracks');
      return emptyData;
    }

    try {
      console.log(`Fetching top tracks for artist ${artistId} with limit ${limit} and time range ${timeRange}`);

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout (increased for multiple time range fetches)

      try {
        const response = await fetch(
          `/.netlify/functions/artist-top-tracks?access_token=${this.accessToken}&artist_id=${artistId}&limit=${limit}&time_range=${timeRange}`,
          { signal: controller.signal }
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to fetch artist top tracks: ${response.status}`, errorText);
          return emptyData;
        }

        let data;
        try {
          data = await response.json();
        } catch (jsonError) {
          console.error('Error parsing JSON response:', jsonError);
          return emptyData;
        }

        console.log(`Successfully fetched ${data.items?.length || 0} top tracks for artist ${artistId}`);

        // Ensure the response has the expected structure
        if (!data || !Array.isArray(data.items)) {
          console.warn('Response missing items array or has invalid structure:', data);
          return { ...emptyData, items: [] };
        }

        // Sanitize the tracks data to ensure all required properties exist
        const sanitizedItems = data.items
          .filter(track => track && typeof track === 'object')
          .map(track => {
            return {
              id: track.id || `unknown-${Math.random().toString(36).substring(2, 9)}`,
              name: track.name || 'Unknown Track',
              album: {
                id: track.album?.id || 'unknown-album',
                name: track.album?.name || 'Unknown Album',
                images: Array.isArray(track.album?.images) ? track.album.images : []
              },
              artists: Array.isArray(track.artists) ? track.artists : [{ id: 'unknown', name: 'Unknown Artist' }],
              duration_ms: typeof track.duration_ms === 'number' ? track.duration_ms : 0,
              popularity: typeof track.popularity === 'number' ? track.popularity : 0,
              preview_url: track.preview_url || null,
              external_urls: track.external_urls || { spotify: '' },
              // Include metadata about time ranges and rank if available
              _timeRanges: track._timeRanges,
              _bestRank: track._bestRank
            };
          });

        return {
          ...emptyData,
          ...data,
          items: sanitizedItems
        };
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError.name === 'AbortError') {
          console.error('Request timed out after 15 seconds');
        } else {
          console.error('Fetch error in getArtistTopTracks:', fetchError);
        }
        return emptyData;
      }
    } catch (error) {
      console.error('Error in getArtistTopTracks:', error);
      // Return empty result instead of throwing to prevent UI crashes
      return emptyData;
    }
  }

  // Create a playlist with top tracks for a specific time range
  async createPlaylist(timeRange: TimeRange, spotifyId: string, playlistName?: string): Promise<SpotifyPlaylist> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    if (!spotifyId) {
      throw new Error('Spotify user ID is required');
    }

    // Validate Spotify ID format
    if (!spotifyId.match(/^[0-9a-zA-Z]+$/)) {
      throw new Error('Invalid Spotify user ID format');
    }

    // Verify the token is valid by making a test API call
    try {
      console.log('Verifying access token with a test API call...');
      const testResponse = await fetch('https://api.spotify.com/v1/me', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (!testResponse.ok) {
        const errorText = await testResponse.text();
        console.error(`Token validation failed with status ${testResponse.status}:`, errorText);
        throw new Error(`Invalid access token: ${testResponse.status} ${errorText}`);
      }

      console.log('Access token verified successfully');
    } catch (tokenError) {
      console.error('Error validating access token:', tokenError);
      throw new Error(`Failed to validate access token: ${tokenError instanceof Error ? tokenError.message : String(tokenError)}`);
    }

    try {
      console.log('Creating playlist with params:', { timeRange, spotifyId, playlistName });

      // Verify we have a valid access token
      console.log('Access token length:', this.accessToken.length);
      console.log('Access token starts with:', this.accessToken.substring(0, 10) + '...');

      // Verify we have a valid Spotify ID
      console.log('Spotify ID:', spotifyId);

      // Add a timestamp to help correlate client and server logs
      const requestTimestamp = new Date().toISOString();
      console.log('Request timestamp:', requestTimestamp);

      // Implement retry logic for the API call
      const maxRetries = 3;
      let retryCount = 0;
      let lastError: Error | null = null;
      let response: Response | null = null;

      // Try direct API call first
      try {
        console.log('Attempting direct Spotify API call first...');
        const directResponse = await fetch(`https://api.spotify.com/v1/users/${spotifyId}/playlists`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: playlistName || `musilize: top tracks (${timeRange === 'short_term' ? 'last month' :
              timeRange === 'medium_term' ? 'last 6 months' : 'all time'})`,
            description: `your top tracks from ${timeRange === 'short_term' ? 'the last month' :
              timeRange === 'medium_term' ? 'the last 6 months' : 'all time'}, created by musilize. [musilize-app-playlist]`,
            public: false
          })
        });

        if (directResponse.ok) {
          const data = await directResponse.json();
          console.log('Direct Spotify API call successful:', data);

          // Return the playlist data in the expected format
          return {
            id: data.id,
            name: data.name,
            external_url: data.external_urls.spotify,
            track_count: 0, // Will be updated when tracks are added
            time_range: timeRange
          };
        } else {
          const errorText = await directResponse.text();
          console.error(`Direct Spotify API call failed with status ${directResponse.status}:`, errorText);
          console.log('Falling back to server function...');
        }
      } catch (directApiError) {
        console.error('Error with direct Spotify API call:', directApiError);
        console.log('Falling back to server function...');
      }

      // Fall back to server function if direct API call fails
      while (retryCount < maxRetries) {
        try {
          console.log(`Attempt ${retryCount + 1} of ${maxRetries} to create playlist via server function`);

          response = await fetch('/.netlify/functions/create-playlist', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              access_token: this.accessToken,
              time_range: timeRange,
              spotify_id: spotifyId,
              playlist_name: playlistName,
              client_timestamp: requestTimestamp,
              retry_count: retryCount,
              use_direct_api: true // Tell the server to try direct API call as well
            }),
          });

          // If we got a response (even an error response), break out of the retry loop
          break;
        } catch (fetchError) {
          // Only retry on network errors, not HTTP errors
          lastError = fetchError instanceof Error ? fetchError : new Error(String(fetchError));
          retryCount++;

          console.error(`Network error on attempt ${retryCount}:`, lastError.message);

          if (retryCount >= maxRetries) {
            console.error(`Max retries (${maxRetries}) reached. Giving up.`);
            throw new Error(`Failed to connect to server after ${maxRetries} attempts: ${lastError.message}`);
          }

          // Wait before retrying (exponential backoff)
          const waitTime = Math.min(1000 * Math.pow(2, retryCount), 10000);
          console.log(`Waiting ${waitTime}ms before retry ${retryCount + 1}...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      if (!response) {
        throw new Error('Failed to get response from server');
      }

      const responseText = await response.text();
      console.log('Response status:', response.status);
      console.log('Response text:', responseText);

      // Handle partial success (207)
      if (response.status === 207) {
        try {
          const data = JSON.parse(responseText);
          console.log('Partial success:', data);

          // Show warning but return the playlist data
          console.warn(data.warning || 'Playlist created with warnings');

          if (data.playlist) {
            return data.playlist;
          }
        } catch (e) {
          console.error('Error parsing partial success response:', e);
        }
      }

      if (!response.ok) {
        let errorMessage = 'Failed to create playlist';
        let errorDetails = '';
        let errorStack = '';

        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorMessage;
          errorDetails = errorData.details || '';
          errorStack = errorData.stack || '';

          console.error('Server error details:', errorData);

          if (errorStack) {
            console.error('Server error stack:', errorStack);
          }
        } catch (e) {
          // If JSON parsing fails, use the raw text
          console.error('Error parsing error response:', e);
          errorMessage = responseText || errorMessage;
        }

        const fullError = errorDetails ? `${errorMessage}: ${errorDetails}` : errorMessage;
        throw new Error(fullError);
      }

      try {
        const data = JSON.parse(responseText);
        console.log('Successfully created playlist response:', data);

        if (!data.playlist) {
          console.error('Response missing playlist data:', data);

          // Check if the data itself might be the playlist
          if (data.id && data.name && data.external_urls) {
            console.log('Found playlist data in root of response');
            return {
              id: data.id,
              name: data.name,
              external_url: data.external_urls.spotify,
              track_count: data.tracks?.total || 0,
              time_range: timeRange
            };
          }

          // Check if there's an error message but the request actually succeeded
          if (data.error === 'Playlist created but missing ID in response' && data.data) {
            console.log('Playlist was created but ID was missing, trying to extract from data');

            // Try to extract ID from the data
            const playlistData = data.data;
            if (playlistData.uri) {
              const uriParts = playlistData.uri.split(':');
              if (uriParts.length === 3 && uriParts[0] === 'spotify' && uriParts[1] === 'playlist') {
                const id = uriParts[2];
                console.log('Extracted playlist ID from URI:', id);
                return {
                  id: id,
                  name: playlistData.name || `Playlist (${timeRange})`,
                  external_url: playlistData.external_urls?.spotify || `https://open.spotify.com/playlist/${id}`,
                  track_count: playlistData.tracks?.total || 0,
                  time_range: timeRange
                };
              }
            }
          }

          throw new Error('Server response missing playlist data');
        }

        return data.playlist;
      } catch (parseError) {
        console.error('Error parsing successful response:', parseError);
        console.error('Raw response text:', responseText);

        // Try to extract playlist ID from the response text as a last resort
        try {
          // Look for patterns that might contain a playlist ID
          const idMatch = responseText.match(/playlist\/([a-zA-Z0-9]{22})/);
          if (idMatch && idMatch[1]) {
            const id = idMatch[1];
            console.log('Extracted playlist ID from response text:', id);
            return {
              id: id,
              name: `Playlist (${timeRange})`,
              external_url: `https://open.spotify.com/playlist/${id}`,
              track_count: 0,
              time_range: timeRange
            };
          }
        } catch (extractError) {
          console.error('Failed to extract playlist ID from response text:', extractError);
        }

        throw new Error('Failed to parse playlist data from server response');
      }
    } catch (error) {
      console.error('Error in createPlaylist:', error);
      throw error;
    }
  }

  // Get user's playlists created by this app
  async getUserPlaylists(spotifyId: string): Promise<SpotifyPlaylist[]> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    try {
      console.log('Fetching playlists for user:', spotifyId);

      const response = await fetch(`/.netlify/functions/get-user-playlists?access_token=${this.accessToken}&spotify_id=${spotifyId}`);

      const responseText = await response.text();
      console.log('Response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Failed to fetch user playlists';
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.details || errorMessage;
        } catch (e) {
          // If JSON parsing fails, use the raw text
          errorMessage = responseText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      try {
        const data = JSON.parse(responseText);
        const playlists = data.playlists || [];
        console.log(`Successfully fetched ${playlists.length} playlists for user ${spotifyId}:`, playlists);

        // If we have playlists, log them for debugging
        if (playlists.length > 0) {
          playlists.forEach((playlist, index) => {
            console.log(`Playlist ${index + 1}:`, {
              id: playlist.id,
              name: playlist.name,
              time_range: playlist.time_range,
              track_count: playlist.track_count
            });
          });
        } else {
          console.log('No playlists found for user', spotifyId);
        }

        return playlists;
      } catch (e) {
        console.error('Error parsing playlist data:', e);
        return [];
      }
    } catch (error) {
      console.error('Error in getUserPlaylists:', error);
      // Return empty array instead of throwing to prevent UI errors
      return [];
    }
  }

  // Update an existing playlist with new top tracks
  async updatePlaylist(playlistId: string, timeRange: TimeRange): Promise<SpotifyPlaylist> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    try {
      console.log('Updating playlist:', { playlistId, timeRange });

      const response = await fetch('/.netlify/functions/update-playlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: this.accessToken,
          playlist_id: playlistId,
          time_range: timeRange,
        }),
      });

      const responseText = await response.text();
      console.log('Response status:', response.status);
      console.log('Response text:', responseText);

      if (!response.ok) {
        let errorMessage = 'Failed to update playlist';
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.details || errorMessage;
        } catch (e) {
          // If JSON parsing fails, use the raw text
          errorMessage = responseText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const data = JSON.parse(responseText);
      return data.playlist;
    } catch (error) {
      console.error('Error in updatePlaylist:', error);
      throw error;
    }
  }

  // Format duration from milliseconds to MM:SS
  static formatDuration(ms: number): string {
    if (typeof ms !== 'number') {
      console.error('Invalid duration value:', ms);
      return '0:00';
    }

    try {
      const minutes = Math.floor(ms / 60000);
      const seconds = Math.floor((ms % 60000) / 1000);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    } catch (error) {
      console.error('Error formatting duration:', error);
      return '0:00';
    }
  }

  // Instance method that calls the static method (for backward compatibility)
  formatDuration(ms: number): string {
    return SpotifyService.formatDuration(ms);
  }

  // Format date to a readable format
  static formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 60) {
      return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
    }

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) {
      return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    }

    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) {
      return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    }

    return date.toLocaleDateString();
  }

  // Add tracks to a playlist directly (client-side fallback)
  async addTracksToPlaylist(playlistId: string, trackUris: string[]): Promise<boolean> {
    if (!this.accessToken) {
      throw new Error('Access token is required');
    }

    if (!playlistId) {
      throw new Error('Playlist ID is required');
    }

    if (!trackUris || trackUris.length === 0) {
      throw new Error('Track URIs are required');
    }

    try {
      console.log(`Adding ${trackUris.length} tracks to playlist ${playlistId}`);

      // Add tracks in batches of 100 (Spotify API limit)
      const batchSize = 100;
      for (let i = 0; i < trackUris.length; i += batchSize) {
        const batch = trackUris.slice(i, i + batchSize);
        console.log(`Adding batch of ${batch.length} tracks (${i + 1} to ${i + batch.length})`);

        const response = await fetch(`https://api.spotify.com/v1/playlists/${playlistId}/tracks`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            uris: batch
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to add tracks to playlist: ${response.status}`, errorText);
          throw new Error(`Failed to add tracks to playlist: ${response.status} ${errorText}`);
        }
      }

      console.log('All tracks added to playlist successfully');
      return true;
    } catch (error) {
      console.error('Error adding tracks to playlist:', error);
      throw error;
    }
  }
}

// Create a singleton instance
export const spotifyService = new SpotifyService();
