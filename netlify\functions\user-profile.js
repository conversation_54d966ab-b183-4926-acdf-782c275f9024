const spotifyApi = require('./utils/spotify');

exports.handler = async function(event, context) {
  try {
    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }
    
    // Get the access token from the query parameters
    const accessToken = event.queryStringParameters.access_token;
    
    if (!accessToken) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Access token is required' })
      };
    }
    
    // Set the access token
    spotifyApi.setAccessToken(accessToken);
    
    // Get the user's profile
    const data = await spotifyApi.getMe();
    
    return {
      statusCode: 200,
      body: JSON.stringify(data.body)
    };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to fetch user profile' })
    };
  }
};
