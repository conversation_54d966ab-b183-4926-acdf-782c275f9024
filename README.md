# Spotify Mirror Insights

A web application that uses the Spotify API to display personalized music insights, including top tracks, top artists, and recently played tracks.

## Features

- **Spotify Authentication**: Login with your Spotify account
- **Top Tracks**: View your most listened tracks over different time periods
- **Top Artists**: Discover your favorite artists based on listening habits
- **Recent Plays**: See your recently played tracks with timestamps
- **Responsive Design**: Works on desktop and mobile devices

## Technologies Used

- **Frontend**: React, TypeScript, Vite, Tailwind CSS, shadcn/ui
- **Backend**: Netlify Functions, Spotify Web API
- **Authentication**: Spotify OAuth
- **Database**: Supabase
- **Deployment**: Netlify
