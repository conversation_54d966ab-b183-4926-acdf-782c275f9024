// CommonJS module for compatibility
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function createSpotifyUsersTable() {
  console.log('Creating spotify_users table in Supabase...');
  
  try {
    // Create the table using SQL query
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS spotify_users (
          id SERIAL PRIMARY KEY,
          spotify_id TEXT UNIQUE NOT NULL,
          display_name TEXT,
          email TEXT,
          profile_image TEXT,
          access_token TEXT NOT NULL,
          refresh_token TEXT NOT NULL,
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    
    if (error) {
      console.error('Error creating table:', error);
      return;
    }
    
    console.log('spotify_users table created successfully!');
  } catch (error) {
    console.error('Error:', error);
  }
}

createSpotifyUsersTable();
