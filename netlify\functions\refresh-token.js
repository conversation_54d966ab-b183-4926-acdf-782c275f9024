const spotifyApi = require('./utils/spotify');
const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

exports.handler = async function (event, context) {
  try {
    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Parse the request body
    const { refresh_token, spotify_id } = JSON.parse(event.body);

    if (!refresh_token) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Refresh token is required' })
      };
    }

    // Set the refresh token
    spotifyApi.setRefreshToken(refresh_token);

    // Refresh the access token
    const data = await spotifyApi.refreshAccessToken();

    // Update the tokens in Supabase if spotify_id is provided
    if (spotify_id) {
      const { error } = await supabase
        .from('spotify_users')
        .update({
          access_token: data.body.access_token,
          expires_at: new Date(Date.now() + data.body.expires_in * 1000).toISOString(),
        })
        .eq('spotify_id', spotify_id);

      if (error) {
        console.error('Error updating tokens in Supabase:', error);
      }
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        access_token: data.body.access_token,
        expires_in: data.body.expires_in
      })
    };
  } catch (error) {
    console.error('Error refreshing token:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to refresh token' })
    };
  }
};
