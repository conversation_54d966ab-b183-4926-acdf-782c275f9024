import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { SpotifyTrack, TimeRange } from '@/services/spotify';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { format } from 'date-fns';
import { toast } from '@/components/ui/sonner';

interface SongRankingModalProps {
  isOpen: boolean;
  onClose: () => void;
  track: SpotifyTrack | null;
  currentPosition: number;
  timeRange: TimeRange;
}

interface RankingHistoryEntry {
  position: number;
  recorded_at: string;
}

interface RankingHistoryResponse {
  history: RankingHistoryEntry[];
  highestPosition: number | null;
}

interface ChartDataPoint {
  date: string;
  position: number;
  formattedDate: string;
}

const SongRankingModal: React.FC<SongRankingModalProps> = ({
  isOpen,
  onClose,
  track,
  currentPosition,
  timeRange,
}) => {
  const { user } = useSpotifyAuth();
  const [loading, setLoading] = useState(true);
  const [historyData, setHistoryData] = useState<ChartDataPoint[]>([]);
  const [highestPosition, setHighestPosition] = useState<number | null>(null);

  useEffect(() => {
    const fetchRankingHistory = async () => {
      if (!track || !user?.id || !isOpen) return;

      try {
        setLoading(true);
        const response = await fetch(
          `/.netlify/functions/track-ranking-history?spotify_id=${user.id}&track_id=${track.id}&time_range=${timeRange}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch ranking history');
        }

        const data: RankingHistoryResponse = await response.json();

        // If there's no history, create a single data point for the current position
        if (!data.history || data.history.length === 0) {
          const now = new Date().toISOString();
          setHistoryData([{
            date: now,
            position: currentPosition,
            formattedDate: format(new Date(now), 'MMM d')
          }]);
        } else {
          // Format the data for the chart
          const chartData = data.history.map(entry => ({
            date: entry.recorded_at,
            position: entry.position,
            formattedDate: format(new Date(entry.recorded_at), 'MMM d')
          }));

          // Add the current position if it's not already in the data
          const lastEntry = chartData[chartData.length - 1];
          const now = new Date();
          const lastDate = lastEntry ? new Date(lastEntry.date) : null;

          // If the last entry is from a different day, add the current position
          if (!lastDate || lastDate.toDateString() !== now.toDateString()) {
            chartData.push({
              date: now.toISOString(),
              position: currentPosition,
              formattedDate: format(now, 'MMM d')
            });
          }

          setHistoryData(chartData);
        }

        // Set the highest position (lowest number is better)
        setHighestPosition(data.highestPosition !== null ? data.highestPosition : currentPosition);
      } catch (error) {
        console.error('Error fetching ranking history:', error);
        // Don't show error toast for missing history data
        // Only log it to the console for debugging

        // Create a fallback data point for the current position
        const now = new Date().toISOString();
        setHistoryData([{
          date: now,
          position: currentPosition,
          formattedDate: format(new Date(now), 'MMM d')
        }]);
        setHighestPosition(currentPosition);
      } finally {
        setLoading(false);
      }
    };

    fetchRankingHistory();
  }, [track, user, isOpen, timeRange, currentPosition]);

  if (!track) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md w-[95%]">
        <DialogHeader>
          <DialogTitle className="text-center">{track.name}</DialogTitle>
          <div className="text-center text-spotify-off-white text-sm">
            {track.artists.map(artist => artist.name).join(', ')}
          </div>
        </DialogHeader>

        <div className="flex items-center space-x-4 mb-4">
          <img
            src={track.album.images[0]?.url || ''}
            alt={track.name}
            className="w-16 h-16 object-cover rounded-md"
          />
          <div>
            <div className="text-sm text-spotify-off-white">Current Position</div>
            <div className="text-2xl font-bold">{currentPosition}</div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-t-spotify-green"></div>
          </div>
        ) : (
          <>
            <div className="h-64 w-full flex justify-center items-center">
              <div className="w-full" style={{ marginLeft: "-15px" }}>
                <ChartContainer
                  config={{
                    line: { color: "#1DB954" }, // Spotify green
                    dot: { color: "#1DB954" },
                    currentDot: { color: "#1DB954" },
                  }}
                  className="h-full w-full"
                >
                  <RechartsPrimitive.LineChart
                    data={historyData}
                    margin={{ top: 20, right: 0, bottom: 20, left: 0 }}
                    className="mx-auto"
                  >
                    <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" stroke="#333" />
                    <RechartsPrimitive.XAxis
                      dataKey="formattedDate"
                      stroke="#999"
                      tick={{ fill: '#999' }}
                    />
                    <RechartsPrimitive.YAxis
                      stroke="#999"
                      tick={{ fill: '#999' }}
                      domain={['dataMin', 'dataMax']}
                      reversed={true} // Higher position numbers are lower on the chart
                      width={30}
                    />
                    <RechartsPrimitive.Tooltip
                      content={({ active, payload }) => {
                        if (active && payload && payload.length) {
                          return (
                            <div className="bg-spotify-dark-gray p-2 rounded border border-spotify-light-gray">
                              <p className="text-white">{`Position: ${payload[0].value}`}</p>
                              <p className="text-spotify-off-white text-xs">{payload[0].payload.formattedDate}</p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <RechartsPrimitive.Line
                      type="monotone"
                      dataKey="position"
                      stroke="#1DB954"
                      strokeWidth={2}
                      dot={{ r: 4, fill: "#1DB954" }}
                      activeDot={{ r: 6, fill: "#1DB954" }}
                    />
                    {/* Blinking dot for current position */}
                    {historyData.length > 0 && (
                      <RechartsPrimitive.Scatter
                        data={[historyData[historyData.length - 1]]}
                        dataKey="position"
                        fill="#1DB954"
                        className="animate-pulse-spotify"
                      >
                        <RechartsPrimitive.Cell r={6} />
                      </RechartsPrimitive.Scatter>
                    )}
                  </RechartsPrimitive.LineChart>
                </ChartContainer>
              </div>
            </div>

            {highestPosition !== null && (
              <div className="text-center mt-4">
                <span className="text-spotify-off-white">highest position: </span>
                <span className="font-bold text-spotify-green">{highestPosition}</span>
              </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default SongRankingModal;
