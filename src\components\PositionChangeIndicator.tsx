import React from 'react';
import { ChevronUp, ChevronDown, Minus, Circle } from 'lucide-react';

export type PositionChangeType = 'up' | 'down' | 'same' | 'new';

interface PositionChangeIndicatorProps {
  changeType: PositionChangeType;
  changeAmount?: number;
}

const PositionChangeIndicator: React.FC<PositionChangeIndicatorProps> = ({
  changeType,
  changeAmount = 0
}) => {
  const getIcon = () => {
    switch (changeType) {
      case 'up':
        return <ChevronUp className="text-spotify-green" size={14} />;
      case 'down':
        return <ChevronDown className="text-red-500" size={14} />;
      case 'same':
        return <Minus className="text-gray-500" size={14} />;
      case 'new':
        return <Circle className="text-blue-500 fill-blue-500" size={10} />;
      default:
        return null;
    }
  };

  const getTooltipText = () => {
    if (changeAmount === 0) return '';

    switch (changeType) {
      case 'up':
        return `Up ${changeAmount} position${changeAmount !== 1 ? 's' : ''}`;
      case 'down':
        return `Down ${changeAmount} position${changeAmount !== 1 ? 's' : ''}`;
      case 'same':
        return 'No change';
      case 'new':
        return 'New entry';
      default:
        return '';
    }
  };

  return (
    <div className="flex items-center justify-center mr-1 w-5" title={getTooltipText()}>
      {getIcon()}
    </div>
  );
};

export default PositionChangeIndicator;
