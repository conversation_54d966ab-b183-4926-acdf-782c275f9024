const spotifyApi = require('./utils/spotify');

exports.handler = async function (event, context) {
  try {
    console.log('Artist top tracks function called with params:', {
      httpMethod: event.httpMethod,
      hasAccessToken: !!event.queryStringParameters?.access_token,
      artistId: event.queryStringParameters?.artist_id,
      timeRange: event.queryStringParameters?.time_range,
      limit: event.queryStringParameters?.limit
    });

    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Get the access token from the query parameters
    const accessToken = event.queryStringParameters?.access_token;

    if (!accessToken) {
      return {
        statusCode: 401,
        body: JSON.stringify({
          error: 'Access token is required',
          items: [],
          total: 0
        })
      };
    }

    // Get the artist ID from the query parameters
    const artistId = event.queryStringParameters?.artist_id;

    if (!artistId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          error: 'Artist ID is required',
          items: [],
          total: 0
        })
      };
    }

    // Get the time range from the query parameters (short_term, medium_term, long_term)
    const timeRange = event.queryStringParameters?.time_range || 'medium_term';

    // Get the limit from the query parameters
    const limit = parseInt(event.queryStringParameters?.limit) || 5;

    try {
      // Set the access token
      spotifyApi.setAccessToken(accessToken);

      // Define all time ranges to fetch
      const timeRanges = ['short_term', 'medium_term', 'long_term'];

      console.log(`Fetching tracks from all time ranges for artist ${artistId}`);

      // First approach: Get the artist's top tracks directly from Spotify
      console.log(`Fetching artist's top tracks directly from Spotify API for artist ${artistId}`);
      let artistTopTracks = [];
      try {
        // Get the artist's top tracks from Spotify API
        const artistTopTracksResponse = await spotifyApi.getArtistTopTracks(artistId, 'US');
        if (artistTopTracksResponse?.body?.tracks && Array.isArray(artistTopTracksResponse.body.tracks)) {
          artistTopTracks = artistTopTracksResponse.body.tracks.map(track => ({
            ...track,
            _timeRange: 'spotify_top',
            _rank: 1
          }));
          console.log(`Found ${artistTopTracks.length} top tracks for artist ${artistId} from Spotify API`);
        }
      } catch (artistTopTracksError) {
        console.error(`Error fetching artist top tracks from Spotify API:`, artistTopTracksError);
        // Continue with the user's top tracks approach if this fails
      }

      // Second approach: Fetch tracks from user's top tracks across all time ranges
      const allTracksPromises = timeRanges.map(async (range) => {
        try {
          console.log(`Fetching user's top tracks for time range: ${range}`);
          const response = await spotifyApi.getMyTopTracks({
            time_range: range,
            limit: 50 // Get a larger set to filter from
          });

          if (response?.body?.items && Array.isArray(response.body.items)) {
            console.log(`Found ${response.body.items.length} tracks for time range ${range}`);
            // Add time range and rank info to each track
            return response.body.items.map((track, index) => ({
              ...track,
              _timeRange: range,
              _rank: index + 1
            }));
          }
          return [];
        } catch (rangeError) {
          console.error(`Error fetching tracks for time range ${range}:`, rangeError);
          return [];
        }
      });

      // Wait for all requests to complete
      const allTracksArrays = await Promise.all(allTracksPromises);

      // Combine all tracks into a single array
      const allTracks = allTracksArrays.flat();

      console.log(`Total tracks fetched from user's top tracks across all time ranges: ${allTracks.length}`);

      // Filter tracks by the specified artist
      const userTopArtistTracks = allTracks.filter(track => {
        try {
          if (!track.artists || !Array.isArray(track.artists)) {
            return false;
          }
          return track.artists.some(artist => artist && artist.id === artistId);
        } catch (filterError) {
          console.error('Error filtering track:', filterError);
          return false;
        }
      });

      console.log(`Found ${userTopArtistTracks.length} tracks for artist ${artistId} in user's top tracks`);

      // Combine artist top tracks from Spotify API with user's top tracks
      const combinedTracks = [...artistTopTracks, ...userTopArtistTracks];

      // If we still have no tracks, try to get the artist's albums and tracks
      if (combinedTracks.length === 0) {
        console.log(`No tracks found for artist ${artistId} in top tracks, fetching albums...`);
        try {
          // Get the artist's albums
          const albumsResponse = await spotifyApi.getArtistAlbums(artistId, { limit: 5, include_groups: 'album,single' });

          if (albumsResponse?.body?.items && Array.isArray(albumsResponse.body.items)) {
            console.log(`Found ${albumsResponse.body.items.length} albums for artist ${artistId}`);

            // Get tracks from each album
            const albumTracksPromises = albumsResponse.body.items.map(async (album) => {
              try {
                const tracksResponse = await spotifyApi.getAlbumTracks(album.id, { limit: 10 });
                if (tracksResponse?.body?.items && Array.isArray(tracksResponse.body.items)) {
                  // Add album info to each track
                  return tracksResponse.body.items.map(track => ({
                    ...track,
                    album: album,
                    _timeRange: 'album_tracks',
                    _rank: 999 // Lower priority than top tracks
                  }));
                }
                return [];
              } catch (albumTracksError) {
                console.error(`Error fetching tracks for album ${album.id}:`, albumTracksError);
                return [];
              }
            });

            const albumTracksArrays = await Promise.all(albumTracksPromises);
            const albumTracks = albumTracksArrays.flat();
            console.log(`Found ${albumTracks.length} tracks from artist's albums`);

            // Add album tracks to combined tracks
            combinedTracks.push(...albumTracks);
          }
        } catch (albumsError) {
          console.error(`Error fetching albums for artist ${artistId}:`, albumsError);
        }
      }

      if (combinedTracks.length === 0) {
        console.log(`No tracks found for artist ${artistId} in any source`);
        return {
          statusCode: 200,
          body: JSON.stringify({
            items: [],
            total: 0,
            message: 'No tracks found for this artist from any source'
          })
        };
      }

      // Create a map to track the best rank for each track
      const trackMap = new Map();

      // Process each track to find its best rank across time ranges
      combinedTracks.forEach(track => {
        if (!track.id) return;

        // If this track is not in the map yet, add it
        if (!trackMap.has(track.id)) {
          // Calculate score based on source and rank
          let score;
          if (track._timeRange === 'spotify_top') {
            score = 10; // High priority for Spotify's top tracks
          } else if (track._timeRange === 'album_tracks') {
            score = 1000 + Math.random() * 100; // Low priority for album tracks with some randomness
          } else {
            // User's top tracks - prioritize by time range and rank
            const timeRangeMultiplier = track._timeRange === timeRange ? 0.5 : 1;
            score = track._rank * timeRangeMultiplier;
          }

          trackMap.set(track.id, {
            track: track,
            bestRank: track._rank,
            timeRanges: [track._timeRange],
            score: score
          });
        } else {
          // Update existing track data if this rank is better
          const existingData = trackMap.get(track.id);

          // Add this time range to the list if not already included
          if (!existingData.timeRanges.includes(track._timeRange)) {
            existingData.timeRanges.push(track._timeRange);
          }

          // Update best rank if this one is better
          if (track._rank < existingData.bestRank) {
            existingData.bestRank = track._rank;
          }

          // Update score if this source is better
          let newScore;
          if (track._timeRange === 'spotify_top') {
            newScore = 10; // High priority
          } else if (track._timeRange === 'album_tracks') {
            newScore = existingData.score; // Keep existing score
          } else {
            // For user's top tracks, use weighted average
            const timeRangeMultiplier = track._timeRange === timeRange ? 0.5 : 1;
            newScore = existingData.score * 0.8 + (track._rank * timeRangeMultiplier) * 0.2;
          }

          // Use the better (lower) score
          existingData.score = Math.min(existingData.score, newScore);
          trackMap.set(track.id, existingData);
        }
      });

      // Convert map back to array and sort by score (lower is better)
      const sortedTracks = Array.from(trackMap.values())
        .sort((a, b) => a.score - b.score)
        .map(item => item.track)
        .slice(0, limit);

      console.log(`Returning top ${sortedTracks.length} tracks for artist ${artistId}`);

      // Sanitize the tracks to ensure they have all required properties
      const sanitizedTracks = sortedTracks.map(track => {
        try {
          return {
            id: track.id || `unknown-${Math.random().toString(36).substring(2, 9)}`,
            name: track.name || 'Unknown Track',
            album: {
              id: track.album?.id || 'unknown-album',
              name: track.album?.name || 'Unknown Album',
              images: Array.isArray(track.album?.images) ? track.album.images : []
            },
            artists: Array.isArray(track.artists) ? track.artists : [{ id: 'unknown', name: 'Unknown Artist' }],
            duration_ms: typeof track.duration_ms === 'number' ? track.duration_ms : 0,
            popularity: typeof track.popularity === 'number' ? track.popularity : 0,
            preview_url: track.preview_url || null,
            external_urls: track.external_urls || { spotify: '' },
            // Include metadata about time ranges and rank
            _timeRanges: track._timeRanges || [track._timeRange],
            _bestRank: track._bestRank || track._rank
          };
        } catch (sanitizeError) {
          console.error('Error sanitizing track:', sanitizeError);
          // Return a default track object if sanitization fails
          return {
            id: `error-${Math.random().toString(36).substring(2, 9)}`,
            name: 'Error Track',
            album: {
              id: 'error-album',
              name: 'Error Album',
              images: []
            },
            artists: [{ id: 'unknown', name: 'Unknown Artist' }],
            duration_ms: 0,
            popularity: 0,
            preview_url: null,
            external_urls: { spotify: '' }
          };
        }
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          items: sanitizedTracks,
          total: sortedTracks.length,
          timeRange: timeRange
        })
      };
    } catch (spotifyError) {
      console.error('Error with Spotify API:', spotifyError);
      return {
        statusCode: 200, // Return 200 with error message instead of 500
        body: JSON.stringify({
          error: 'Error fetching from Spotify API',
          details: spotifyError.message,
          items: [],
          total: 0
        })
      };
    }
  } catch (error) {
    console.error('Unhandled error in artist-top-tracks function:', error);
    return {
      statusCode: 200, // Return 200 with error message instead of 500
      body: JSON.stringify({
        error: 'Failed to fetch artist top tracks',
        details: error.message,
        items: [],
        total: 0
      })
    };
  }
};
