import React, { useState, useMemo } from 'react';
import SpotifyLayout from '../components/SpotifyLayout';
import TimeRangeSelector from '../components/TimeRangeSelector';
import { useTopArtists } from '@/hooks/use-spotify-data';
import { TimeRange } from '@/services/spotify';
import { Music } from 'lucide-react';

interface GenreCount {
  name: string;
  count: number;
  artists: string[];
}

const Genres = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('medium_term');

  // Fetch top artists using the custom hook
  const { data: topArtistsData, isLoading } = useTopArtists(timeRange, 50);

  // Process genres from artists data
  const genreCounts = useMemo(() => {
    if (!topArtistsData?.items) return [];

    const genreMap = new Map<string, GenreCount>();

    // Count occurrences of each genre and track artists
    topArtistsData.items.forEach(artist => {
      artist.genres.forEach(genre => {
        if (!genreMap.has(genre)) {
          genreMap.set(genre, { name: genre, count: 0, artists: [] });
        }

        const genreData = genreMap.get(genre)!;
        genreData.count += 1;

        // Only add artist if not already in the list
        if (!genreData.artists.includes(artist.name)) {
          genreData.artists.push(artist.name);
        }
      });
    });

    // Convert map to array and sort by count (descending)
    return Array.from(genreMap.values())
      .sort((a, b) => b.count - a.count);
  }, [topArtistsData]);

  return (
    <SpotifyLayout>
      <div>
        <h1 className="text-3xl font-bold mb-6">top genres</h1>
        <TimeRangeSelector selected={timeRange} onChange={setTimeRange} />

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
          </div>
        ) : (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {genreCounts.map((genre, index) => (
              <div
                key={genre.name}
                className="bg-spotify-dark-gray rounded-lg p-4 hover:bg-spotify-light-gray transition-colors"
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="bg-spotify-green/20 rounded-full p-2">
                    <Music size={20} className="text-spotify-green" />
                  </div>
                  <div className="w-full">
                    <h3 className="font-bold text-lg break-words" title={genre.name}>{genre.name}</h3>
                    <p className="text-spotify-off-white text-sm">{genre.count} artists</p>
                  </div>
                </div>
                <div className="text-sm text-spotify-off-white">
                  <p>top artists: {genre.artists.slice(0, 3).join(', ')}{genre.artists.length > 3 ? '...' : ''}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </SpotifyLayout>
  );
};

export default Genres;
