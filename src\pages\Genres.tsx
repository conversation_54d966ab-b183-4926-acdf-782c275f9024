import React, { useState, useMemo, useEffect } from 'react';
import SpotifyLayout from '../components/SpotifyLayout';
import TimeRangeSelector from '../components/TimeRangeSelector';
import GenreRankingModal from '../components/GenreRankingModal';
import { useTopArtists } from '@/hooks/use-spotify-data';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { TimeRange } from '@/services/spotify';
import { Music, TrendingUp } from 'lucide-react';

interface GenreCount {
  name: string;
  count: number;
  artists: string[];
}

const Genres = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('medium_term');
  const [selectedGenre, setSelectedGenre] = useState<string | null>(null);
  const [selectedPosition, setSelectedPosition] = useState<number>(0);
  const [selectedCount, setSelectedCount] = useState<number>(0);
  const [isRankingModalOpen, setIsRankingModalOpen] = useState<boolean>(false);
  const { user } = useSpotifyAuth();

  // Fetch top artists using the custom hook
  const { data: topArtistsData, isLoading } = useTopArtists(timeRange, 50);

  // Process genres from artists data
  const genreCounts = useMemo(() => {
    if (!topArtistsData?.items) return [];

    const genreMap = new Map<string, GenreCount>();

    // Count occurrences of each genre and track artists
    topArtistsData.items.forEach(artist => {
      artist.genres.forEach(genre => {
        if (!genreMap.has(genre)) {
          genreMap.set(genre, { name: genre, count: 0, artists: [] });
        }

        const genreData = genreMap.get(genre)!;
        genreData.count += 1;

        // Only add artist if not already in the list
        if (!genreData.artists.includes(artist.name)) {
          genreData.artists.push(artist.name);
        }
      });
    });

    // Convert map to array and sort by count (descending)
    return Array.from(genreMap.values())
      .sort((a, b) => b.count - a.count);
  }, [topArtistsData]);

  // Store genre ranking data when genres are loaded
  useEffect(() => {
    const storeGenreRankings = async () => {
      if (!genreCounts.length || !user?.id) return;

      try {
        await fetch('/.netlify/functions/genre-ranking-history', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            genres: genreCounts,
            time_range: timeRange,
            spotify_id: user.id,
          }),
        });
      } catch (error) {
        console.error('Error storing genre rankings:', error);
        // Don't show an error toast to the user as this is a background operation
      }
    };

    storeGenreRankings();
  }, [genreCounts, timeRange, user]);

  const handleGenreClick = (genre: GenreCount, position: number) => {
    setSelectedGenre(genre.name);
    setSelectedPosition(position);
    setSelectedCount(genre.count);
    setIsRankingModalOpen(true);
  };

  return (
    <SpotifyLayout>
      <div>
        <h1 className="text-3xl font-bold mb-6">top genres</h1>
        <TimeRangeSelector selected={timeRange} onChange={setTimeRange} />

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
          </div>
        ) : (
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {genreCounts.map((genre, index) => (
              <div
                key={genre.name}
                className="bg-spotify-dark-gray rounded-lg p-4 hover:bg-spotify-light-gray transition-colors cursor-pointer group"
                onClick={() => handleGenreClick(genre, index + 1)}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="bg-spotify-green/20 rounded-full p-2">
                    <Music size={20} className="text-spotify-green" />
                  </div>
                  <div className="w-full">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-bold text-lg break-words" title={genre.name}>#{index + 1} {genre.name}</h3>
                        <p className="text-spotify-off-white text-sm">{genre.count} artists</p>
                      </div>
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <TrendingUp size={16} className="text-spotify-green" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-sm text-spotify-off-white">
                  <p>top artists: {genre.artists.slice(0, 3).join(', ')}{genre.artists.length > 3 ? '...' : ''}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Genre Ranking Modal */}
        <GenreRankingModal
          isOpen={isRankingModalOpen}
          onClose={() => setIsRankingModalOpen(false)}
          genreName={selectedGenre}
          currentPosition={selectedPosition}
          currentCount={selectedCount}
          timeRange={timeRange}
        />
      </div>
    </SpotifyLayout>
  );
};

export default Genres;
