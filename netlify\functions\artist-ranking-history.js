const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
let supabase;
try {
  supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);
} catch (error) {
  console.error('Error initializing Supabase client:', error);
}

exports.handler = async function (event, context) {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (!supabase) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Database connection not available' })
    };
  }

  if (event.httpMethod === 'GET') {
    // Get artist ranking history
    const { spotify_id, artist_id, time_range } = event.queryStringParameters || {};

    if (!spotify_id || !artist_id || !time_range) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: spotify_id, artist_id, time_range' })
      };
    }

    try {
      // Ensure the artist_ranking_history table exists
      try {
        await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS artist_ranking_history (
              id SERIAL PRIMARY KEY,
              spotify_id TEXT NOT NULL,
              artist_id TEXT NOT NULL,
              position INTEGER NOT NULL,
              time_range TEXT NOT NULL,
              recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              -- Foreign key constraint removed to avoid dependency on spotify_users table
            );

            CREATE INDEX IF NOT EXISTS idx_artist_history_spotify_id ON artist_ranking_history(spotify_id);
            CREATE INDEX IF NOT EXISTS idx_artist_history_artist_id ON artist_ranking_history(artist_id);
            CREATE INDEX IF NOT EXISTS idx_artist_history_artist_user ON artist_ranking_history(spotify_id, artist_id);
          `
        });
      } catch (error) {
        console.log('Table already exists or error creating table:', error);
        // Continue anyway
      }

      // Query the artist ranking history
      const { data, error } = await supabase
        .from('artist_ranking_history')
        .select('position, recorded_at')
        .eq('spotify_id', spotify_id)
        .eq('artist_id', artist_id)
        .eq('time_range', time_range)
        .order('recorded_at', { ascending: true });

      if (error) {
        console.error('Error retrieving artist ranking history:', error);
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to retrieve artist ranking history' })
        };
      }

      // Get the highest (lowest number) position
      let highestPosition = null;
      if (data && data.length > 0) {
        highestPosition = Math.min(...data.map(entry => entry.position));
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          history: data || [],
          highestPosition
        })
      };
    } catch (error) {
      console.error('Error in artist ranking history GET:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Internal server error' })
      };
    }
  }

  if (event.httpMethod === 'POST') {
    // Store artist ranking data
    try {
      const { artists, time_range, spotify_id } = JSON.parse(event.body);

      if (!artists || !time_range || !spotify_id) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Missing required fields: artists, time_range, spotify_id' })
        };
      }

      // Ensure the artist_ranking_history table exists
      try {
        await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS artist_ranking_history (
              id SERIAL PRIMARY KEY,
              spotify_id TEXT NOT NULL,
              artist_id TEXT NOT NULL,
              position INTEGER NOT NULL,
              time_range TEXT NOT NULL,
              recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              -- Foreign key constraint removed to avoid dependency on spotify_users table
            );

            CREATE INDEX IF NOT EXISTS idx_artist_history_spotify_id ON artist_ranking_history(spotify_id);
            CREATE INDEX IF NOT EXISTS idx_artist_history_artist_id ON artist_ranking_history(artist_id);
            CREATE INDEX IF NOT EXISTS idx_artist_history_artist_user ON artist_ranking_history(spotify_id, artist_id);
          `
        });
      } catch (error) {
        console.log('Table already exists or error creating table:', error);
        // Continue anyway
      }

      // Prepare the data for insertion
      const artistData = artists.map((artist, index) => ({
        spotify_id,
        artist_id: artist.id,
        position: index + 1,
        time_range,
        recorded_at: new Date().toISOString()
      }));

      // Insert the artist ranking data
      const { error } = await supabase
        .from('artist_ranking_history')
        .insert(artistData);

      if (error) {
        console.error('Error storing artist ranking history:', error);
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to store artist ranking history' })
        };
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ message: 'Artist ranking history stored successfully' })
      };
    } catch (error) {
      console.error('Error in artist ranking history POST:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Internal server error' })
      };
    }
  }

  return {
    statusCode: 405,
    headers,
    body: JSON.stringify({ error: 'Method not allowed' })
  };
};
