const spotifyApi = require('./utils/spotify');
const { createClient } = require('@supabase/supabase-js');
const { baseUrl, supabaseConfig } = require('./config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

exports.handler = async function (event, context) {
  try {
    // Get the authorization code from the query parameters
    const code = event.queryStringParameters.code;

    if (!code) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Authorization code is missing' })
      };
    }

    // Exchange the code for an access token and refresh token
    const data = await spotifyApi.authorizationCodeGrant(code);

    // Set the access token and refresh token
    spotifyApi.setAccessToken(data.body.access_token);
    spotifyApi.setRefreshToken(data.body.refresh_token);

    // Get the user's profile
    const userProfile = await spotifyApi.getMe();

    // Store the tokens and user profile in Supabase
    const { error } = await supabase
      .from('spotify_users')
      .upsert({
        spotify_id: userProfile.body.id,
        display_name: userProfile.body.display_name,
        email: userProfile.body.email,
        profile_image: userProfile.body.images?.[0]?.url || null,
        access_token: data.body.access_token,
        refresh_token: data.body.refresh_token,
        expires_at: new Date(Date.now() + data.body.expires_in * 1000).toISOString(),
      });

    if (error) {
      console.error('Error storing user data in Supabase:', error);
    }

    // Redirect back to the frontend with the tokens
    return {
      statusCode: 302,
      headers: {
        Location: `/?access_token=${data.body.access_token}&refresh_token=${data.body.refresh_token}&expires_in=${data.body.expires_in}&spotify_id=${userProfile.body.id}`,
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({ message: 'Authentication successful' })
    };
  } catch (error) {
    console.error('Error during Spotify callback:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to authenticate with Spotify' })
    };
  }
};
