// CommonJS module for compatibility
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function createUserPlaylistsTable() {
  console.log('Creating user_playlists table in Supabase...');
  
  try {
    // Create the table using SQL query
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS user_playlists (
          id SERIAL PRIMARY KEY,
          spotify_id TEXT NOT NULL,
          playlist_id TEXT UNIQUE NOT NULL,
          playlist_name TEXT NOT NULL,
          time_range TEXT NOT NULL,
          track_count INTEGER NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          FOREIGN KEY (spotify_id) REFERENCES spotify_users(spotify_id) ON DELETE CASCADE
        );
        
        -- Create an index on spotify_id for faster lookups
        CREATE INDEX IF NOT EXISTS idx_user_playlists_spotify_id ON user_playlists(spotify_id);
      `
    });
    
    if (error) {
      console.error('Error creating table:', error);
      return;
    }
    
    console.log('user_playlists table created successfully!');
  } catch (error) {
    console.error('Error:', error);
  }
}

createUserPlaylistsTable();
