const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
let supabase;
try {
  supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);
} catch (error) {
  console.error('Error initializing Supabase client:', error);
}

exports.handler = async function (event, context) {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (!supabase) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Database connection not available' })
    };
  }

  // Only allow POST and GET requests
  if (event.httpMethod !== 'POST' && event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Get the Spotify ID from the query parameters
    const spotify_id = event.queryStringParameters?.spotify_id;

    if (!spotify_id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Spotify ID is required' })
      };
    }

    // Handle GET request - retrieve track ranking history
    if (event.httpMethod === 'GET') {
      const track_id = event.queryStringParameters?.track_id;
      const time_range = event.queryStringParameters?.time_range || 'medium_term';

      if (!track_id) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Track ID is required' })
        };
      }

      // Try to query the table first to see if it exists
      try {
        await supabase
          .from('track_ranking_history')
          .select('id')
          .limit(1);
      } catch (error) {
        console.log('Table might not exist, but continuing anyway:', error);
        // Table might not exist, but we'll continue and let the query fail gracefully
      }

      // Query the track ranking history
      const { data, error } = await supabase
        .from('track_ranking_history')
        .select('position, recorded_at')
        .eq('spotify_id', spotify_id)
        .eq('track_id', track_id)
        .eq('time_range', time_range)
        .order('recorded_at', { ascending: true });

      if (error) {
        console.error('Error retrieving track ranking history:', error);
        // If table doesn't exist, return empty data instead of error
        if (error.code === 'PGRST106' || error.message.includes('does not exist')) {
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
              history: [],
              highestPosition: null
            })
          };
        }
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to retrieve track ranking history' })
        };
      }

      // Get the highest (lowest number) position
      let highestPosition = null;
      if (data && data.length > 0) {
        highestPosition = Math.min(...data.map(entry => entry.position));
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          history: data || [],
          highestPosition: highestPosition
        })
      };
    }

    // Handle POST request - store track ranking data
    if (event.httpMethod === 'POST') {
      const body = JSON.parse(event.body);
      const { tracks, time_range } = body;

      if (!tracks || !Array.isArray(tracks)) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Tracks data is required and must be an array' })
        };
      }

      if (!time_range) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Time range is required' })
        };
      }

      // Try to query the table first to see if it exists
      try {
        await supabase
          .from('track_ranking_history')
          .select('id')
          .limit(1);
      } catch (error) {
        console.log('Table might not exist, but continuing anyway:', error);
        // Table might not exist, but we'll continue and let the insert fail gracefully
      }

      // Prepare the data for insertion
      const trackData = tracks.map((track, index) => ({
        spotify_id,
        track_id: track.id,
        position: index + 1,
        time_range,
        recorded_at: new Date().toISOString()
      }));

      // Insert the track ranking data
      const { error } = await supabase
        .from('track_ranking_history')
        .insert(trackData);

      if (error) {
        console.error('Error storing track ranking history:', error);
        // If table doesn't exist, just log it and return success
        if (error.code === 'PGRST106' || error.message.includes('does not exist')) {
          console.log('Track ranking history table does not exist yet. Data will be stored once table is created.');
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ message: 'Track ranking history will be stored once table is created' })
          };
        }
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to store track ranking history' })
        };
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ message: 'Track ranking history stored successfully' })
      };
    }
  } catch (error) {
    console.error('Error in track ranking history:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
