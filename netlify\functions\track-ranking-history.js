const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

exports.handler = async function (event, context) {
  // Only allow POST and GET requests
  if (event.httpMethod !== 'POST' && event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Get the Spotify ID from the query parameters
    const spotify_id = event.queryStringParameters.spotify_id;

    if (!spotify_id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Spotify ID is required' })
      };
    }

    // Handle GET request - retrieve track ranking history
    if (event.httpMethod === 'GET') {
      const track_id = event.queryStringParameters.track_id;
      const time_range = event.queryStringParameters.time_range || 'medium_term';

      if (!track_id) {
        return {
          statusCode: 400,
          body: JSON.stringify({ error: 'Track ID is required' })
        };
      }

      // Ensure the track_ranking_history table exists
      try {
        await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS track_ranking_history (
              id SERIAL PRIMARY KEY,
              spotify_id TEXT NOT NULL,
              track_id TEXT NOT NULL,
              position INTEGER NOT NULL,
              time_range TEXT NOT NULL,
              recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              -- Foreign key constraint removed to avoid dependency on spotify_users table
            );

            CREATE INDEX IF NOT EXISTS idx_track_history_spotify_id ON track_ranking_history(spotify_id);
            CREATE INDEX IF NOT EXISTS idx_track_history_track_id ON track_ranking_history(track_id);
            CREATE INDEX IF NOT EXISTS idx_track_history_track_user ON track_ranking_history(spotify_id, track_id);
          `
        });
      } catch (error) {
        console.log('Table already exists or error creating table:', error);
        // Continue anyway
      }

      // Query the track ranking history
      const { data, error } = await supabase
        .from('track_ranking_history')
        .select('position, recorded_at')
        .eq('spotify_id', spotify_id)
        .eq('track_id', track_id)
        .eq('time_range', time_range)
        .order('recorded_at', { ascending: true });

      if (error) {
        console.error('Error retrieving track ranking history:', error);
        return {
          statusCode: 500,
          body: JSON.stringify({ error: 'Failed to retrieve track ranking history' })
        };
      }

      // Get the highest (lowest number) position
      let highestPosition = null;
      if (data && data.length > 0) {
        highestPosition = Math.min(...data.map(entry => entry.position));
      }

      return {
        statusCode: 200,
        body: JSON.stringify({
          history: data || [],
          highestPosition: highestPosition
        })
      };
    }

    // Handle POST request - store track ranking data
    if (event.httpMethod === 'POST') {
      const body = JSON.parse(event.body);
      const { tracks, time_range } = body;

      if (!tracks || !Array.isArray(tracks)) {
        return {
          statusCode: 400,
          body: JSON.stringify({ error: 'Tracks data is required and must be an array' })
        };
      }

      if (!time_range) {
        return {
          statusCode: 400,
          body: JSON.stringify({ error: 'Time range is required' })
        };
      }

      // Ensure the track_ranking_history table exists
      try {
        await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS track_ranking_history (
              id SERIAL PRIMARY KEY,
              spotify_id TEXT NOT NULL,
              track_id TEXT NOT NULL,
              position INTEGER NOT NULL,
              time_range TEXT NOT NULL,
              recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              -- Foreign key constraint removed to avoid dependency on spotify_users table
            );

            CREATE INDEX IF NOT EXISTS idx_track_history_spotify_id ON track_ranking_history(spotify_id);
            CREATE INDEX IF NOT EXISTS idx_track_history_track_id ON track_ranking_history(track_id);
            CREATE INDEX IF NOT EXISTS idx_track_history_track_user ON track_ranking_history(spotify_id, track_id);
          `
        });
      } catch (error) {
        console.log('Table already exists or error creating table:', error);
        // Continue anyway
      }

      // Prepare the data for insertion
      const trackData = tracks.map((track, index) => ({
        spotify_id,
        track_id: track.id,
        position: index + 1,
        time_range,
        recorded_at: new Date().toISOString()
      }));

      // Insert the track ranking data
      const { error } = await supabase
        .from('track_ranking_history')
        .insert(trackData);

      if (error) {
        console.error('Error storing track ranking history:', error);
        return {
          statusCode: 500,
          body: JSON.stringify({ error: 'Failed to store track ranking history' })
        };
      }

      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Track ranking history stored successfully' })
      };
    }
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
