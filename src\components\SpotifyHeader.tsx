
import React from 'react';
import { ChevronLeft, ChevronRight, User, LogOut, Menu } from 'lucide-react';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { SidebarTrigger, useSidebar } from '@/components/ui/sidebar';

const SpotifyHeader: React.FC = () => {
  const { user, logout } = useSpotifyAuth();
  const navigate = useNavigate();
  const { isMobile } = useSidebar();

  const goBack = () => {
    navigate(-1);
  };

  const goForward = () => {
    navigate(1);
  };

  return (
    <header className="sticky top-0 z-10 flex items-center justify-between bg-spotify-black/95 backdrop-blur-sm px-4 py-4">
      <div className="flex items-center gap-2">
        {isMobile && (
          <SidebarTrigger className="mr-1 text-white">
            <Menu size={20} />
          </SidebarTrigger>
        )}
        <button
          onClick={goBack}
          className="bg-black/70 rounded-full p-1 text-white hover:bg-spotify-light-gray transition-colors"
        >
          <ChevronLeft size={20} />
        </button>
        <button
          onClick={goForward}
          className="bg-black/70 rounded-full p-1 text-white hover:bg-spotify-light-gray transition-colors"
        >
          <ChevronRight size={20} />
        </button>
      </div>

      <div className="flex items-center gap-2">
        {user && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center gap-2 bg-black/40 rounded-full p-1 pr-3 hover:bg-black/70 transition-colors">
                <Avatar className="h-7 w-7">
                  <AvatarImage src={user.profileImage || "https://source.unsplash.com/featured/?person"} />
                  <AvatarFallback>
                    <User size={16} />
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium text-white">{user.displayName || user.id}</span>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-spotify-dark-gray border-zinc-700">
              <DropdownMenuItem
                onClick={() => logout()}
                className="cursor-pointer text-white hover:bg-spotify-light-gray flex items-center gap-2"
              >
                <LogOut size={16} className="text-spotify-off-white" />
                <span>log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
};

export default SpotifyHeader;
