
import React, { useState } from 'react';
import SpotifyLayout from '../components/SpotifyLayout';
import TimeRangeSelector from '../components/TimeRangeSelector';
import ArtistCard from '../components/ArtistCard';
import ArtistTopTracksModal from '../components/ArtistTopTracksModal';
import { useTopArtists } from '@/hooks/use-spotify-data';
import { TimeRange, SpotifyArtist } from '@/services/spotify';

const TopArtists = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('medium_term');
  const [selectedArtist, setSelectedArtist] = useState<SpotifyArtist | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  // Fetch top artists using the custom hook
  const { data: topArtistsData, isLoading } = useTopArtists(timeRange, 50);

  const handleArtistClick = (artist: SpotifyArtist) => {
    try {
      console.log('Artist clicked on TopArtists page:', {
        id: artist?.id || 'missing-id',
        name: artist?.name || 'missing-name'
      });

      // Create a sanitized copy of the artist data with fallbacks for all properties
      const sanitizedArtist: SpotifyArtist = {
        id: artist?.id || `unknown-${Date.now()}`,
        name: artist?.name || 'Unknown Artist',
        images: Array.isArray(artist?.images) ? artist.images : [],
        genres: Array.isArray(artist?.genres) ? artist.genres : [],
        popularity: typeof artist?.popularity === 'number' ? artist.popularity : 0,
        external_urls: artist?.external_urls || { spotify: '' }
      };

      setSelectedArtist(sanitizedArtist);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error handling artist click:', error);
      // Show a fallback artist if there's an error
      setSelectedArtist({
        id: 'error',
        name: 'Error Loading Artist',
        images: [],
        genres: [],
        popularity: 0,
        external_urls: { spotify: '' }
      });
      setIsModalOpen(true);
    }
  };

  return (
    <SpotifyLayout>
      <div>
        <h1 className="text-3xl font-bold mb-6">top artists</h1>
        <TimeRangeSelector selected={timeRange} onChange={setTimeRange} />

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {topArtistsData?.items.map((artist, index) => (
              <ArtistCard
                key={artist.id}
                name={artist.name}
                imageUrl={artist.images[0]?.url || ''}
                rank={index + 1}
                artist={artist}
                onClick={handleArtistClick}
              />
            ))}
          </div>
        )}

        {/* Artist Top Tracks Modal */}
        <ArtistTopTracksModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          artist={selectedArtist}
          timeRange={timeRange}
        />
      </div>
    </SpotifyLayout>
  );
};

export default TopArtists;
