import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Clock, BarChart, Calendar } from 'lucide-react';
import { TimeRange } from '@/services/spotify';

interface RankingExplanationModalProps {
  isOpen: boolean;
  onClose: () => void;
  timeRange: TimeRange;
}

const RankingExplanationModal: React.FC<RankingExplanationModalProps> = ({
  isOpen,
  onClose,
  timeRange,
}) => {
  // Get time range text for display
  const getTimeRangeText = (range: TimeRange): string => {
    switch (range) {
      case 'short_term':
        return 'last 4 weeks';
      case 'medium_term':
        return 'last 6 months';
      case 'long_term':
        return 'all time';
      default:
        return 'selected time range';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md w-[95%]">
        <DialogHeader>
          <DialogTitle className="text-center">How Tracks Are Ranked</DialogTitle>
        </DialogHeader>

        <div className="space-y-5 py-2 text-spotify-off-white">
          <p className="text-sm bg-spotify-light-gray/20 p-3 rounded-md border border-spotify-light-gray/30">
            The tracks shown are your most played songs with this artist from your entire listening history, not just the current time frame.
          </p>

          <div className="space-y-3 mt-4">
            <div className="flex items-start gap-3 p-2 hover:bg-spotify-light-gray/10 rounded-md transition-colors">
              <div className="bg-spotify-green/20 p-2 rounded-full">
                <BarChart size={18} className="text-spotify-green" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-white">Multiple Data Sources</h3>
                <p className="text-xs text-spotify-off-white">
                  We combine data from your personal listening history, the artist's most popular tracks, and their albums to ensure you always see tracks.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-2 hover:bg-spotify-light-gray/10 rounded-md transition-colors">
              <div className="bg-spotify-green/20 p-2 rounded-full">
                <Calendar size={18} className="text-spotify-green" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-white">Your Listening Habits</h3>
                <p className="text-xs text-spotify-off-white">
                  Tracks you've listened to frequently across multiple time periods (4 weeks, 6 months, all time) are ranked higher.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-2 hover:bg-spotify-light-gray/10 rounded-md transition-colors">
              <div className="bg-spotify-green/20 p-2 rounded-full">
                <Clock size={18} className="text-spotify-green" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-white">Artist Popularity</h3>
                <p className="text-xs text-spotify-off-white">
                  If you haven't listened to many tracks from this artist, we'll show their most popular tracks according to Spotify.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4 bg-spotify-light-gray/30 p-3 rounded-md border border-spotify-light-gray/50">
            <h3 className="text-sm font-medium text-white mb-2">Track Source Indicators</h3>
            <div className="grid grid-cols-2 gap-2 text-xs mb-2">
              <div className="flex items-center gap-1 bg-spotify-light-gray/20 p-2 rounded">
                <span className="text-spotify-green font-medium">4w</span>
                <span className="text-spotify-off-white">= Last 4 weeks</span>
              </div>
              <div className="flex items-center gap-1 bg-spotify-light-gray/20 p-2 rounded">
                <span className="text-spotify-green font-medium">6m</span>
                <span className="text-spotify-off-white">= Last 6 months</span>
              </div>
              <div className="flex items-center gap-1 bg-spotify-light-gray/20 p-2 rounded">
                <span className="text-spotify-green font-medium">all</span>
                <span className="text-spotify-off-white">= All time</span>
              </div>
              <div className="flex items-center gap-1 bg-spotify-light-gray/20 p-2 rounded">
                <span className="text-spotify-green font-medium">top</span>
                <span className="text-spotify-off-white">= Artist's top tracks</span>
              </div>
            </div>
            <p className="text-xs text-spotify-off-white mt-2">
              Some tracks may appear in multiple time ranges, which means they're consistently among your favorites.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RankingExplanationModal;
