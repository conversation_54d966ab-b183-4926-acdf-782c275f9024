
import React from 'react';
import { SpotifyArtist } from '@/services/spotify';

interface ArtistCardProps {
  name: string;
  imageUrl: string;
  rank?: number;
  artist?: SpotifyArtist;
  onClick?: (artist: SpotifyArtist) => void;
}

const ArtistCard: React.FC<ArtistCardProps> = ({ name, imageUrl, rank, artist, onClick }) => {
  const handleClick = () => {
    if (onClick && artist) {
      onClick(artist);
    }
  };

  return (
    <div
      className={`spotify-card group relative ${artist ? 'cursor-pointer hover:bg-spotify-light-gray' : ''}`}
      onClick={handleClick}
    >
      <div className="relative">
        <img
          src={imageUrl}
          alt={name}
          className="w-full aspect-square object-cover rounded-full shadow-lg mb-4"
        />
        {rank && (
          <div className="absolute top-2 left-2 bg-black/70 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
            {rank}
          </div>
        )}
        {artist && (
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-full">
            {/* Shading effect on hover */}
          </div>
        )}
      </div>
      <h3 className="font-bold text-base text-center truncate group-hover:text-spotify-green transition-colors">{name}</h3>
      <p className="text-spotify-off-white text-sm text-center">Artist</p>
    </div>
  );
};

export default ArtistCard;
