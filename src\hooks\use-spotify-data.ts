import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  spotifyService,
  TimeRange,
  SpotifyTrack,
  SpotifyArtist,
  SpotifyPlayHistory,
  SpotifyPaging,
  SpotifyPlaylist
} from '@/services/spotify';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { toast } from '@/components/ui/sonner';

// Hook to fetch top tracks
export function useTopTracks(timeRange: TimeRange = 'medium_term', limit: number = 20) {
  const { tokens } = useSpotifyAuth();

  return useQuery<SpotifyPaging<SpotifyTrack>>({
    queryKey: ['topTracks', timeRange, limit],
    queryFn: async () => {
      if (tokens?.accessToken) {
        spotifyService.setAccessToken(tokens.accessToken);
        return await spotifyService.getTopTracks(timeRange, limit);
      }
      throw new Error('No access token available');
    },
    enabled: !!tokens?.accessToken,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook to fetch top artists
export function useTopArtists(timeRange: TimeRange = 'medium_term', limit: number = 20) {
  const { tokens } = useSpotifyAuth();

  return useQuery<SpotifyPaging<SpotifyArtist>>({
    queryKey: ['topArtists', timeRange, limit],
    queryFn: async () => {
      if (tokens?.accessToken) {
        spotifyService.setAccessToken(tokens.accessToken);
        return await spotifyService.getTopArtists(timeRange, limit);
      }
      throw new Error('No access token available');
    },
    enabled: !!tokens?.accessToken,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook to fetch recently played tracks
export function useRecentlyPlayed(limit: number = 20) {
  const { tokens } = useSpotifyAuth();

  return useQuery<SpotifyPaging<SpotifyPlayHistory>>({
    queryKey: ['recentlyPlayed', limit],
    queryFn: async () => {
      if (tokens?.accessToken) {
        spotifyService.setAccessToken(tokens.accessToken);
        return await spotifyService.getRecentlyPlayed(limit);
      }
      throw new Error('No access token available');
    },
    enabled: !!tokens?.accessToken,
    staleTime: 1000 * 60 * 1, // 1 minute (shorter because this data changes more frequently)
  });
}

// Hook to fetch artist's top tracks
export function useArtistTopTracks(artistId: string | undefined, limit: number = 5, timeRange: TimeRange = 'medium_term') {
  const { tokens } = useSpotifyAuth();

  // Create a fallback empty data structure
  const emptyData: SpotifyPaging<SpotifyTrack> = {
    items: [],
    total: 0,
    limit: 0,
    href: '',
    next: null,
    offset: 0,
    previous: null
  };

  return useQuery<SpotifyPaging<SpotifyTrack>>({
    queryKey: ['artistTopTracks', artistId, limit, timeRange],
    queryFn: async () => {
      // Log the artist ID to help with debugging
      console.log(`Fetching top tracks for artist ID: ${artistId} with time range: ${timeRange}`);

      if (!tokens?.accessToken) {
        console.error('No access token available for artist top tracks');
        return emptyData;
      }

      if (!artistId) {
        console.error('No artist ID provided for artist top tracks');
        return emptyData;
      }

      try {
        spotifyService.setAccessToken(tokens.accessToken);
        const result = await spotifyService.getArtistTopTracks(artistId, limit, timeRange);
        console.log(`Successfully fetched ${result.items?.length || 0} top tracks for artist ${artistId} with time range ${timeRange}`);

        // Validate the result structure
        if (!result || !Array.isArray(result.items)) {
          console.error('Invalid result structure from getArtistTopTracks:', result);
          return {
            ...emptyData,
            items: []
          };
        }

        // Sanitize the tracks data to ensure all required properties exist
        const sanitizedItems = result.items.map(track => {
          if (!track) return null;

          return {
            id: track.id || `unknown-${Math.random().toString(36).substring(2, 9)}`,
            name: track.name || 'Unknown Track',
            album: {
              id: track.album?.id || 'unknown-album',
              name: track.album?.name || 'Unknown Album',
              images: Array.isArray(track.album?.images) ? track.album.images : []
            },
            artists: Array.isArray(track.artists) ? track.artists : [{ id: 'unknown', name: 'Unknown Artist' }],
            duration_ms: typeof track.duration_ms === 'number' ? track.duration_ms : 0,
            popularity: typeof track.popularity === 'number' ? track.popularity : 0,
            preview_url: track.preview_url || null,
            external_urls: track.external_urls || { spotify: '' },
            // Include metadata about time ranges and rank if available
            _timeRanges: track._timeRanges,
            _bestRank: track._bestRank
          };
        }).filter(Boolean) as SpotifyTrack[];

        return {
          ...result,
          items: sanitizedItems
        };
      } catch (error) {
        console.error('Error fetching artist top tracks:', error);
        // Return empty result instead of throwing to prevent UI crashes
        return emptyData;
      }
    },
    enabled: !!tokens?.accessToken && !!artistId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2, // Retry failed requests twice
    // Return empty data instead of undefined when query is disabled
    placeholderData: emptyData
  });
}

// Hook to fetch user playlists
export function useUserPlaylists() {
  const { tokens, user } = useSpotifyAuth();
  const queryClient = useQueryClient();

  return useQuery<SpotifyPlaylist[]>({
    queryKey: ['userPlaylists'],
    queryFn: async () => {
      if (tokens?.accessToken && user?.id) {
        spotifyService.setAccessToken(tokens.accessToken);
        console.log(`Fetching playlists for user ${user.id}...`);
        const playlists = await spotifyService.getUserPlaylists(user.id);
        console.log(`Fetched ${playlists.length} playlists for user ${user.id}`);
        return playlists;
      }
      throw new Error('No access token or user ID available');
    },
    enabled: !!tokens?.accessToken && !!user?.id,
    staleTime: 1000 * 30, // 30 seconds (reduced to ensure more frequent updates)
    cacheTime: 1000 * 60 * 60, // 1 hour (keep in cache longer)
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true,
    retry: 3, // Retry failed requests 3 times
  });
}

// Hook to create a playlist
export function useCreatePlaylist() {
  const { tokens, user } = useSpotifyAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      timeRange,
      playlistName
    }: {
      timeRange: TimeRange;
      playlistName?: string;
    }) => {
      console.log('useCreatePlaylist - Current tokens:', tokens);
      console.log('useCreatePlaylist - Current user:', user);

      if (!tokens?.accessToken) {
        console.error('No access token available');
        throw new Error('No access token available. Please log in again.');
      }

      if (!user?.id) {
        console.error('No user ID available');
        throw new Error('No user ID available. Please log in again.');
      }

      spotifyService.setAccessToken(tokens.accessToken);
      console.log(`Creating playlist for user ${user.id} with time range ${timeRange}`);
      return await spotifyService.createPlaylist(timeRange, user.id, playlistName);
    },
    onSuccess: async (data) => {
      console.log('Playlist created successfully:', data);

      // Add the new playlist directly to the cache
      const currentPlaylists = queryClient.getQueryData<any[]>(['userPlaylists']) || [];

      // Check if the playlist is already in the cache
      const playlistExists = currentPlaylists.some(p => p.id === data.id);

      if (!playlistExists) {
        console.log('Adding new playlist to cache:', data);

        // Add the new playlist to the cache with created_at and last_updated_at fields
        const enhancedData = {
          ...data,
          created_at: new Date().toISOString(),
          last_updated_at: new Date().toISOString()
        };

        // Add the new playlist to the cache
        queryClient.setQueryData(['userPlaylists'], [
          enhancedData,
          ...currentPlaylists
        ]);
      }

      // Invalidate the userPlaylists query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['userPlaylists'] });

      // Wait a moment for the database to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Force an immediate refetch
      await queryClient.refetchQueries({ queryKey: ['userPlaylists'] });

      toast.success('Playlist created successfully!');
    },
    onError: (error) => {
      console.error('Error creating playlist:', error);
      toast.error(`Failed to create playlist: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });
}

// Hook to update a playlist
export function useUpdatePlaylist() {
  const { tokens } = useSpotifyAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      playlistId,
      timeRange
    }: {
      playlistId: string;
      timeRange: TimeRange;
    }) => {
      if (!tokens?.accessToken) {
        throw new Error('No access token available');
      }

      spotifyService.setAccessToken(tokens.accessToken);
      return await spotifyService.updatePlaylist(playlistId, timeRange);
    },
    onSuccess: () => {
      // Invalidate the userPlaylists query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['userPlaylists'] });
      // Force an immediate refetch
      queryClient.refetchQueries({ queryKey: ['userPlaylists'] });
      toast.success('Playlist updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating playlist:', error);
      toast.error(`Failed to update playlist: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });
}
