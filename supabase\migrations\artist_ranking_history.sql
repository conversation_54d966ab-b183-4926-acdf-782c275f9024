-- Create the artist_ranking_history table
CREATE TABLE IF NOT EXISTS artist_ranking_history (
  id SERIAL PRIMARY KEY,
  spotify_id TEXT NOT NULL,
  artist_id TEXT NOT NULL,
  position INTEGER NOT NULL,
  time_range TEXT NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (spotify_id) REFERENCES spotify_users(spotify_id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_artist_history_spotify_id ON artist_ranking_history(spotify_id);
CREATE INDEX IF NOT EXISTS idx_artist_history_artist_id ON artist_ranking_history(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_history_artist_user ON artist_ranking_history(spotify_id, artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_history_time_range ON artist_ranking_history(time_range);
CREATE INDEX IF NOT EXISTS idx_artist_history_recorded_at ON artist_ranking_history(recorded_at);
