const spotifyApi = require('./utils/spotify');

exports.handler = async function(event, context) {
  try {
    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }
    
    // Get the access token from the query parameters
    const accessToken = event.queryStringParameters.access_token;
    
    if (!accessToken) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Access token is required' })
      };
    }
    
    // Get the limit from the query parameters
    const limit = parseInt(event.queryStringParameters.limit) || 20;
    
    // Set the access token
    spotifyApi.setAccessToken(accessToken);
    
    // Get the user's recently played tracks
    const data = await spotifyApi.getMyRecentlyPlayedTracks({
      limit: limit
    });
    
    return {
      statusCode: 200,
      body: JSON.stringify(data.body)
    };
  } catch (error) {
    console.error('Error fetching recently played tracks:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to fetch recently played tracks' })
    };
  }
};
