const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('../config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

// Create a function to create a table if it doesn't exist
async function createTableIfNotExists(tableName, tableDefinition) {
  try {
    // Check if the table exists
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', tableName);

    if (error) {
      console.error('Error checking if table exists:', error);
      return false;
    }

    // If the table doesn't exist, create it
    if (!data || data.length === 0) {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS ${tableName} (
          ${tableDefinition}
        );
      `;

      const { error: createError } = await supabase.rpc('exec_sql', {
        query: createTableQuery
      });

      if (createError) {
        console.error(`Error creating table ${tableName}:`, createError);
        return false;
      }

      console.log(`Table ${tableName} created successfully`);
      return true;
    }

    console.log(`Table ${tableName} already exists`);
    return true;
  } catch (error) {
    console.error('Error in createTableIfNotExists:', error);
    return false;
  }
}

module.exports = {
  createTableIfNotExists
};
