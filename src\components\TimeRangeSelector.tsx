
import React from 'react';
import { TimeRange } from '@/services/spotify';

interface TimeRangeSelectorProps {
  selected: TimeRange;
  onChange: (value: TimeRange) => void;
}

const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({ selected, onChange }) => {
  return (
    <div className="flex gap-2 mb-6">
      <button
        onClick={() => onChange('short_term')}
        className={`px-4 py-1 rounded-full text-sm font-bold transition-colors ${selected === 'short_term'
          ? 'bg-white text-black'
          : 'bg-transparent text-white border border-gray-500 hover:border-white'
          }`}
      >
        last month
      </button>
      <button
        onClick={() => onChange('medium_term')}
        className={`px-4 py-1 rounded-full text-sm font-bold transition-colors ${selected === 'medium_term'
          ? 'bg-white text-black'
          : 'bg-transparent text-white border border-gray-500 hover:border-white'
          }`}
      >
        last 6 months
      </button>
      <button
        onClick={() => onChange('long_term')}
        className={`px-4 py-1 rounded-full text-sm font-bold transition-colors ${selected === 'long_term'
          ? 'bg-white text-black'
          : 'bg-transparent text-white border border-gray-500 hover:border-white'
          }`}
      >
        all time
      </button>
    </div>
  );
};

export default TimeRangeSelector;
