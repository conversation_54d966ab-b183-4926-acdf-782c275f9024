const spotifyApi = require('./utils/spotify');
const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

exports.handler = async function (event, context) {
  try {
    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Get the access token and spotify_id from the query parameters
    const accessToken = event.queryStringParameters.access_token;
    const spotifyId = event.queryStringParameters.spotify_id;

    if (!accessToken) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Access token is required' })
      };
    }

    if (!spotifyId) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Spotify ID is required' })
      };
    }

    // Set the access token
    spotifyApi.setAccessToken(accessToken);

    // Get the user's playlists directly from Spotify
    console.log('Fetching playlists directly from Spotify API for user:', spotifyId);

    try {
      // First check if the table exists using a safer approach
      try {
        // Try to create the table if it doesn't exist
        await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS user_playlists (
              id SERIAL PRIMARY KEY,
              spotify_id TEXT NOT NULL,
              playlist_id TEXT UNIQUE NOT NULL,
              playlist_name TEXT NOT NULL,
              time_range TEXT NOT NULL,
              track_count INTEGER NOT NULL,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            CREATE INDEX IF NOT EXISTS idx_user_playlists_spotify_id ON user_playlists(spotify_id);
          `
        });

        console.log('Table check/creation completed successfully');
      } catch (tableError) {
        console.error('Error creating user_playlists table if needed:', tableError);
        // Continue anyway - we'll try to query the table and handle any errors
      }

      // Get all user playlists from Spotify
      const spotifyPlaylists = await spotifyApi.getUserPlaylists(spotifyId, { limit: 50 });
      console.log(`Found ${spotifyPlaylists.body.total} total playlists for user ${spotifyId}`);

      // Filter playlists to find those created by our app
      // Look for our unique identifier in the description
      const appPlaylists = spotifyPlaylists.body.items.filter(playlist => {
        return playlist.description && playlist.description.includes('musilize-app-playlist');
      });

      console.log(`Found ${appPlaylists.length} playlists created by our app`);

      // Get time range from playlist description or name
      const getTimeRange = (playlist) => {
        // Default to medium_term if we can't determine
        let timeRange = 'medium_term';

        // Check the name first
        const name = playlist.name.toLowerCase();
        if (name.includes('last month')) {
          timeRange = 'short_term';
        } else if (name.includes('6 months') || name.includes('six months')) {
          timeRange = 'medium_term';
        } else if (name.includes('all time')) {
          timeRange = 'long_term';
        }

        // Also check the database for this playlist
        return timeRange;
      };

      // Map the playlists to our format
      const playlistDetails = appPlaylists.map(playlist => {
        const timeRange = getTimeRange(playlist);
        const now = new Date().toISOString();

        return {
          id: playlist.id,
          name: playlist.name,
          external_url: playlist.external_urls.spotify,
          track_count: playlist.tracks.total,
          time_range: timeRange,
          created_at: now, // We don't have this info from Spotify API
          last_updated_at: now // We don't have this info from Spotify API
        };
      });

      // Also check the database for any playlists we know about
      try {
        const { data, error } = await supabase
          .from('user_playlists')
          .select('*')
          .eq('spotify_id', spotifyId);

        if (error) {
          console.error('Error fetching playlists from Supabase:', error);
        } else if (data && data.length > 0) {
          console.log(`Found ${data.length} playlists in database`);

          // Update time ranges and dates from database if available
          for (const dbPlaylist of data) {
            const matchingPlaylist = playlistDetails.find(p => p.id === dbPlaylist.playlist_id);
            if (matchingPlaylist) {
              matchingPlaylist.time_range = dbPlaylist.time_range;
              matchingPlaylist.created_at = dbPlaylist.created_at;
              matchingPlaylist.last_updated_at = dbPlaylist.last_updated_at;
            }
          }
        }
      } catch (dbError) {
        console.error('Error querying database for playlists:', dbError);
      }

      // Return the playlists we found
      return {
        statusCode: 200,
        body: JSON.stringify({ playlists: playlistDetails })
      };
    } catch (spotifyError) {
      console.error('Error fetching playlists from Spotify:', spotifyError);

      // Try to fall back to database if Spotify API fails
      try {
        const { data, error } = await supabase
          .from('user_playlists')
          .select('*')
          .eq('spotify_id', spotifyId);

        if (error) {
          console.error('Error fetching playlists from Supabase:', error);
          return {
            statusCode: 200,
            body: JSON.stringify({ playlists: [] })
          };
        }

        if (data && data.length > 0) {
          console.log(`Falling back to ${data.length} playlists from database`);

          const dbPlaylists = data.map(playlist => ({
            id: playlist.playlist_id,
            name: playlist.playlist_name,
            external_url: `https://open.spotify.com/playlist/${playlist.playlist_id}`,
            track_count: playlist.track_count,
            time_range: playlist.time_range,
            created_at: playlist.created_at,
            last_updated_at: playlist.last_updated_at
          }));

          return {
            statusCode: 200,
            body: JSON.stringify({ playlists: dbPlaylists })
          };
        }
      } catch (dbError) {
        console.error('Error querying database for fallback playlists:', dbError);
      }

      // If all else fails, return empty array
      return {
        statusCode: 200,
        body: JSON.stringify({ playlists: [] })
      };
    }
  } catch (error) {
    console.error('Error fetching user playlists:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to fetch user playlists', details: error.message })
    };
  }
};
