export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      airports: {
        Row: {
          continent: string | null
          elevation_ft: number | null
          gps_code: string | null
          home_link: string | null
          iata_code: string | null
          icao_code: string | null
          id: number
          ident: string | null
          iso_country: string | null
          iso_region: string | null
          keywords: string | null
          latitude_deg: number | null
          local_code: string | null
          longitude_deg: number | null
          municipality: string | null
          name: string | null
          scheduled_service: string | null
          type: string | null
          wikipedia_link: string | null
        }
        Insert: {
          continent?: string | null
          elevation_ft?: number | null
          gps_code?: string | null
          home_link?: string | null
          iata_code?: string | null
          icao_code?: string | null
          id: number
          ident?: string | null
          iso_country?: string | null
          iso_region?: string | null
          keywords?: string | null
          latitude_deg?: number | null
          local_code?: string | null
          longitude_deg?: number | null
          municipality?: string | null
          name?: string | null
          scheduled_service?: string | null
          type?: string | null
          wikipedia_link?: string | null
        }
        Update: {
          continent?: string | null
          elevation_ft?: number | null
          gps_code?: string | null
          home_link?: string | null
          iata_code?: string | null
          icao_code?: string | null
          id?: number
          ident?: string | null
          iso_country?: string | null
          iso_region?: string | null
          keywords?: string | null
          latitude_deg?: number | null
          local_code?: string | null
          longitude_deg?: number | null
          municipality?: string | null
          name?: string | null
          scheduled_service?: string | null
          type?: string | null
          wikipedia_link?: string | null
        }
        Relationships: []
      }
      devupdates: {
        Row: {
          created_at: string | null
          email: string
          id: number
          name: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: number
          name?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: number
          name?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      poi_markers: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          lat: number
          lng: number
          name: string
          photos: string[] | null
          updated_at: string | null
          wikiname: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: number
          lat: number
          lng: number
          name: string
          photos?: string[] | null
          updated_at?: string | null
          wikiname?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: number
          lat?: number
          lng?: number
          name?: string
          photos?: string[] | null
          updated_at?: string | null
          wikiname?: string | null
        }
        Relationships: []
      }
      poisuggestions: {
        Row: {
          id: number
          poi_location: string
          poi_name: string
          status: string | null
          submission_date: string | null
          user_email: string
          user_name: string
        }
        Insert: {
          id?: number
          poi_location: string
          poi_name: string
          status?: string | null
          submission_date?: string | null
          user_email: string
          user_name: string
        }
        Update: {
          id?: number
          poi_location?: string
          poi_name?: string
          status?: string | null
          submission_date?: string | null
          user_email?: string
          user_name?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          id: string
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          id: string
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          id?: string
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      user_markers: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          ispoi: boolean | null
          lat: number
          lng: number
          name: string
          placename: string | null
          updated_at: string | null
          user_id: string
          wikiname: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: number
          ispoi?: boolean | null
          lat: number
          lng: number
          name: string
          placename?: string | null
          updated_at?: string | null
          user_id: string
          wikiname?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: number
          ispoi?: boolean | null
          lat?: number
          lng?: number
          name?: string
          placename?: string | null
          updated_at?: string | null
          user_id?: string
          wikiname?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
