/* Import custom scrollbar styles */
@import './styles/scrollbar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 100%;

    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 100%;

    --primary: 142 72% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 16%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 16%;
    --muted-foreground: 0 0% 70%;

    --accent: 142 72% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 16%;
    --input: 0 0% 16%;
    --ring: 142 72% 50%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 142 72% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 15%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 142 72% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-spotify-black text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Spotify-like scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: #535353;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #7e7e7e;
  }
}

@layer components {
  .spotify-card {
    @apply bg-spotify-dark-gray rounded-md p-4 hover:bg-spotify-light-gray transition-all duration-300 hover:scale-[1.02] hover:shadow-lg;
    background-image: linear-gradient(to bottom right, rgba(40, 40, 40, 0.3), rgba(20, 20, 20, 0.5));
  }

  .playlist-card {
    @apply bg-spotify-dark-gray rounded-md overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-lg border border-transparent hover:border-spotify-green/20;
    background-image: linear-gradient(to bottom right, rgba(40, 40, 40, 0.3), rgba(20, 20, 20, 0.5));
  }

  .playlist-card-header {
    @apply p-5 border-b border-spotify-light-gray/30;
  }

  .playlist-card-content {
    @apply p-5;
  }

  .playlist-card-footer {
    @apply p-4 flex justify-between items-center bg-spotify-light-gray/20;
  }

  .spotify-hover {
    @apply hover:bg-spotify-light-gray transition-colors duration-300;
  }

  .sidebar-link-active {
    @apply bg-spotify-green text-white font-medium;
    box-shadow: 0 0 10px rgba(29, 185, 84, 0.3);
    position: relative;
    overflow: hidden;
  }

  .sidebar-link-active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
    opacity: 0.5;
  }
}