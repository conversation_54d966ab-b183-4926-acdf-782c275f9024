const spotifyApi = require('./utils/spotify');

// Scopes for Spotify API access
const SCOPES = [
  'user-read-private',
  'user-read-email',
  'user-top-read',
  'user-read-recently-played',
  'user-library-read',
  'playlist-read-private',
  'playlist-read-collaborative',
  'playlist-modify-public',
  'playlist-modify-private',
];

exports.handler = async function (event, context) {
  try {
    // Generate a random state value for security
    const state = Math.random().toString(36).substring(2, 15);

    // Generate the authorization URL
    const authorizeURL = spotifyApi.createAuthorizeURL(SCOPES, state);

    // Redirect to Spotify login
    return {
      statusCode: 302,
      headers: {
        Location: authorizeURL,
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify({ message: 'Redirecting to Spotify login' })
    };
  } catch (error) {
    console.error('Error during Spotify login:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to initiate Spotify login' })
    };
  }
};
