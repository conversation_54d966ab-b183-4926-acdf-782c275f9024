// This script runs before the build to ensure environment variables are properly set
require('dotenv').config();

// Check for required environment variables
const requiredEnvVars = [
  'SPOTIFY_CLIENT_ID',
  'SPOTIFY_CLIENT_SECRET',
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(`Error: Missing required environment variables: ${missingEnvVars.join(', ')}`);
  console.error('Please set these environment variables in your Netlify dashboard or .env file.');
  process.exit(1);
}

// Set client-side environment variables if they're not already set
if (!process.env.VITE_SUPABASE_URL && process.env.SUPABASE_URL) {
  process.env.VITE_SUPABASE_URL = process.env.SUPABASE_URL;
  console.log('Set VITE_SUPABASE_URL from SUPABASE_URL');
}

if (!process.env.VITE_SUPABASE_ANON_KEY && process.env.SUPABASE_ANON_KEY) {
  process.env.VITE_SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
  console.log('Set VITE_SUPABASE_ANON_KEY from SUPABASE_ANON_KEY');
}

// Set the Spotify redirect URI for production if not already set
if (!process.env.SPOTIFY_REDIRECT_URI && process.env.URL) {
  process.env.SPOTIFY_REDIRECT_URI = `${process.env.URL}/.netlify/functions/callback`;
  console.log(`Set SPOTIFY_REDIRECT_URI to ${process.env.SPOTIFY_REDIRECT_URI}`);
}

console.log('Environment variables validated successfully.');
