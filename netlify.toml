[build]
  command = "npm run build"
  publish = "dist"
  functions = "netlify/functions"

[[plugins]]
  package = "."
  # Using CommonJS plugin file

[dev]
  command = "npm run vite:dev"
  port = 8888
  targetPort = 8080
  publish = "dist"
  autoLaunch = true
  framework = "#custom"

# Development environment variables
[context.dev.environment]
  NODE_ENV = "development"

# Production environment variables
[context.production.environment]
  NODE_ENV = "production"
  # Note: Sensitive environment variables like SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET,
  # SUPABASE_URL, SUPABASE_ANON_KEY, VITE_SUPABASE_URL, and VITE_SUPABASE_ANON_KEY
  # should be set in the Netlify UI under Site settings > Build & deploy > Environment

# Redirects
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
