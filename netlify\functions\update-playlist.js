const spotifyApi = require('./utils/spotify');
const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

exports.handler = async function (event, context) {
  try {
    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Parse the request body
    const { access_token, playlist_id, time_range, limit = 50 } = JSON.parse(event.body);

    if (!access_token) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Access token is required' })
      };
    }

    if (!playlist_id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Playlist ID is required' })
      };
    }

    if (!time_range) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Time range is required' })
      };
    }

    // Set the access token
    spotifyApi.setAccessToken(access_token);

    // Get the user's top tracks for the specified time range
    const topTracksData = await spotifyApi.getMyTopTracks({
      time_range: time_range,
      limit: limit
    });

    if (!topTracksData.body.items || topTracksData.body.items.length === 0) {
      return {
        statusCode: 404,
        body: JSON.stringify({ error: 'No tracks found for the specified time range' })
      };
    }

    // Extract track URIs
    const trackUris = topTracksData.body.items.map(track => track.uri);

    // Get the current playlist to check if it exists
    const playlist = await spotifyApi.getPlaylist(playlist_id);

    // Replace all tracks in the playlist
    await spotifyApi.replaceTracksInPlaylist(playlist_id, trackUris);

    // Try to update the playlist in Supabase
    try {
      // First check if the table exists
      const { data: tableExists, error: tableCheckError } = await supabase
        .from('user_playlists')
        .select('count(*)', { count: 'exact', head: true })
        .limit(0);

      if (tableCheckError) {
        console.error('Error checking if user_playlists table exists:', tableCheckError);
        // If table doesn't exist, we'll create it
        if (tableCheckError.code === '42P01') { // PostgreSQL code for undefined_table
          console.log('Table does not exist, creating it...');
          await supabase.rpc('exec_sql', {
            sql: `
              CREATE TABLE IF NOT EXISTS user_playlists (
                id SERIAL PRIMARY KEY,
                spotify_id TEXT NOT NULL,
                playlist_id TEXT UNIQUE NOT NULL,
                playlist_name TEXT NOT NULL,
                time_range TEXT NOT NULL,
                track_count INTEGER NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
              CREATE INDEX IF NOT EXISTS idx_user_playlists_spotify_id ON user_playlists(spotify_id);
            `
          });
        }
      }

      // Now update the playlist data
      const { error } = await supabase
        .from('user_playlists')
        .update({
          track_count: trackUris.length,
          last_updated_at: new Date().toISOString()
        })
        .eq('playlist_id', playlist_id);

      if (error) {
        console.error('Error updating playlist in Supabase:', error);
      }
    } catch (dbError) {
      console.error('Database operation failed:', dbError);
      // Continue anyway since the playlist was updated in Spotify
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        playlist: {
          id: playlist.body.id,
          name: playlist.body.name,
          external_url: playlist.body.external_urls.spotify,
          track_count: trackUris.length,
          time_range: time_range,
          updated: true
        }
      })
    };
  } catch (error) {
    console.error('Error updating playlist:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to update playlist', details: error.message })
    };
  }
};
