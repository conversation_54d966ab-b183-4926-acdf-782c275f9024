import React, { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from '@/components/ui/sonner';
import { supabase } from '@/integrations/supabase/client';
import { spotifyService, TimeRange } from '@/services/spotify';

interface SpotifyTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  spotifyId: string;
}

interface SpotifyUser {
  id: string;
  displayName: string;
  email: string;
  profileImage: string | null;
}

interface SpotifyAuthContextProps {
  tokens: SpotifyTokens | null;
  user: SpotifyUser | null;
  loading: boolean;
  loginWithSpotify: () => void;
  logout: () => Promise<void>;
}

const SpotifyAuthContext = createContext<SpotifyAuthContextProps | undefined>(undefined);

export const SpotifyAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [tokens, setTokens] = useState<SpotifyTokens | null>(null);
  const [user, setUser] = useState<SpotifyUser | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // Function to refresh the access token
  const refreshAccessToken = async (refreshToken: string, spotifyId: string) => {
    try {
      const response = await fetch('/.netlify/functions/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken, spotify_id: spotifyId }),
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const data = await response.json();

      // Update the tokens
      setTokens(prev => {
        if (!prev) return null;

        const newExpiresAt = new Date(Date.now() + data.expires_in * 1000);

        return {
          ...prev,
          accessToken: data.access_token,
          expiresAt: newExpiresAt,
        };
      });

      return data.access_token;
    } catch (error) {
      console.error('Error refreshing token:', error);
      // If we can't refresh the token, log the user out
      await logout();
      return null;
    }
  };

  // Function to fetch the user profile
  const fetchUserProfile = async (accessToken: string) => {
    try {
      const response = await fetch(`/.netlify/functions/user-profile?access_token=${accessToken}`);

      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }

      const data = await response.json();

      const userInfo = {
        id: data.id,
        displayName: data.display_name,
        email: data.email,
        profileImage: data.images?.[0]?.url || null,
      };

      setUser(userInfo);

      // After setting the user, check and update playlists
      if (accessToken && userInfo.id) {
        updateUserPlaylists(accessToken, userInfo.id);
      }

      return userInfo;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  };

  // Function to check and update user playlists
  const updateUserPlaylists = async (accessToken: string, userId: string) => {
    try {
      // Set the access token for the Spotify service
      spotifyService.setAccessToken(accessToken);

      // Get the user's playlists
      const playlists = await spotifyService.getUserPlaylists(userId);

      if (playlists.length === 0) {
        // No playlists to update
        console.log('No playlists found to update');
        return;
      }

      console.log(`Found ${playlists.length} playlists to check for updates`);

      // Check if any playlists need updating (last updated more than 12 hours ago by default)
      // This can be adjusted based on user preferences
      const now = new Date();
      const updateInterval = 12 * 60 * 60 * 1000; // 12 hours in milliseconds
      const updateThreshold = new Date(now.getTime() - updateInterval);

      // Filter playlists that need updating
      const playlistsToUpdate = playlists.filter(playlist =>
        !playlist.last_updated_at || new Date(playlist.last_updated_at) < updateThreshold
      );

      if (playlistsToUpdate.length === 0) {
        console.log('All playlists are up to date');
        return;
      }

      // Notify the user that playlists are being updated
      if (playlistsToUpdate.length > 0) {
        toast.info(`Updating ${playlistsToUpdate.length} playlist(s) with your latest top tracks...`, {
          duration: 5000,
          id: 'playlist-update'
        });
      }

      // Update each playlist that needs updating
      for (const playlist of playlistsToUpdate) {
        try {
          console.log(`Updating playlist: ${playlist.name} (${playlist.time_range})`);

          // Show a more specific toast for each playlist
          toast.loading(`Updating "${playlist.name}"...`, {
            id: `update-${playlist.id}`,
            duration: 3000
          });

          // Update the playlist
          await spotifyService.updatePlaylist(playlist.id, playlist.time_range as TimeRange);

          // Show success toast
          toast.success(`Updated "${playlist.name}" with your latest top tracks!`, {
            id: `update-${playlist.id}`,
            duration: 3000
          });

          // Small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (playlistError) {
          console.error(`Error updating playlist ${playlist.name}:`, playlistError);

          // Show error toast
          toast.error(`Failed to update "${playlist.name}". Try updating it manually.`, {
            id: `update-${playlist.id}`,
            duration: 5000
          });
        }
      }

      // Final success message
      if (playlistsToUpdate.length > 0) {
        toast.success(`Finished updating ${playlistsToUpdate.length} playlist(s)!`, {
          id: 'playlist-update',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('Error updating playlists:', error);
      toast.error('Failed to check for playlist updates. Try again later.');
    }
  };

  // Check for tokens in URL parameters (after Spotify callback)
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const accessToken = params.get('access_token');
    const refreshToken = params.get('refresh_token');
    const expiresIn = params.get('expires_in');
    const spotifyId = params.get('spotify_id');

    if (accessToken && refreshToken && expiresIn && spotifyId) {
      // Calculate when the token expires
      const expiresAt = new Date(Date.now() + parseInt(expiresIn) * 1000);

      // Save the tokens
      setTokens({
        accessToken,
        refreshToken,
        expiresAt,
        spotifyId,
      });

      // Store tokens in localStorage
      localStorage.setItem('spotify_tokens', JSON.stringify({
        accessToken,
        refreshToken,
        expiresAt,
        spotifyId,
      }));

      // Fetch the user profile
      fetchUserProfile(accessToken);

      // Remove the tokens from the URL
      navigate('/', { replace: true });

      toast.success('Successfully logged in with Spotify');
    }

    setLoading(false);
  }, [location.search, navigate]);

  // Check for tokens in localStorage on initial load
  useEffect(() => {
    const storedTokens = localStorage.getItem('spotify_tokens');
    console.log('Checking for stored tokens:', storedTokens ? 'Found' : 'Not found');

    if (storedTokens) {
      try {
        const parsedTokens = JSON.parse(storedTokens);
        console.log('Parsed tokens:', parsedTokens);

        // Convert expiresAt string back to Date object
        parsedTokens.expiresAt = new Date(parsedTokens.expiresAt);

        // Check if the token is expired or about to expire (within 5 minutes)
        const isExpired = parsedTokens.expiresAt.getTime() - Date.now() < 5 * 60 * 1000;
        console.log('Token expired?', isExpired, 'Expires at:', parsedTokens.expiresAt);

        if (isExpired) {
          console.log('Token is expired, refreshing...');
          // Refresh the token
          refreshAccessToken(parsedTokens.refreshToken, parsedTokens.spotifyId)
            .then(newAccessToken => {
              if (newAccessToken) {
                console.log('Token refreshed successfully');
                // Fetch the user profile with the new token
                fetchUserProfile(newAccessToken);
              } else {
                console.error('Failed to refresh token');
                // Clear invalid tokens
                localStorage.removeItem('spotify_tokens');
                setTokens(null);
                setUser(null);
              }
              setLoading(false);
            })
            .catch(error => {
              console.error('Error refreshing token:', error);
              // Clear invalid tokens
              localStorage.removeItem('spotify_tokens');
              setTokens(null);
              setUser(null);
              setLoading(false);
            });
        } else {
          // Token is still valid
          console.log('Token is still valid');
          setTokens(parsedTokens);
          fetchUserProfile(parsedTokens.accessToken);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error parsing stored tokens:', error);
        localStorage.removeItem('spotify_tokens');
        setTokens(null);
        setUser(null);
        setLoading(false);
      }
    } else {
      console.log('No stored tokens found');
      setLoading(false);
    }
  }, []);

  // Set up a timer to refresh the token before it expires
  useEffect(() => {
    if (!tokens) return;

    // Calculate time until token expires (minus 5 minutes for safety)
    const timeUntilExpiry = tokens.expiresAt.getTime() - Date.now() - 5 * 60 * 1000;

    if (timeUntilExpiry <= 0) {
      // Token is already expired or about to expire, refresh it immediately
      refreshAccessToken(tokens.refreshToken, tokens.spotifyId);
      return;
    }

    // Set up a timer to refresh the token
    const refreshTimer = setTimeout(() => {
      refreshAccessToken(tokens.refreshToken, tokens.spotifyId);
    }, timeUntilExpiry);

    return () => clearTimeout(refreshTimer);
  }, [tokens]);

  const loginWithSpotify = () => {
    window.location.href = '/.netlify/functions/login';
  };

  const logout = async () => {
    try {
      // Clear tokens from state and localStorage
      setTokens(null);
      setUser(null);
      localStorage.removeItem('spotify_tokens');

      // Redirect to the home page (which will show the login screen)
      navigate('/');

      toast.success('Successfully logged out');
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error('Error logging out');
    }
  };

  return (
    <SpotifyAuthContext.Provider value={{ tokens, user, loading, loginWithSpotify, logout }}>
      {children}
    </SpotifyAuthContext.Provider>
  );
};

export const useSpotifyAuth = () => {
  const context = useContext(SpotifyAuthContext);
  if (context === undefined) {
    throw new Error('useSpotifyAuth must be used within a SpotifyAuthProvider');
  }
  return context;
};
