#!/usr/bin/env node
// @ts-check

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if .env file exists
if (!fs.existsSync(path.join(__dirname, '.env'))) {
  console.error('Error: .env file not found. Please create a .env file with your Spotify API credentials.');
  process.exit(1);
}

// Check if Netlify functions directory exists
if (!fs.existsSync(path.join(__dirname, 'netlify', 'functions'))) {
  console.error('Error: netlify/functions directory not found. Please make sure the directory exists.');
  process.exit(1);
}

console.log('Starting development server...');

try {
  // Run the Netlify dev server
  execSync('npx netlify dev', { stdio: 'inherit' });
} catch (error) {
  console.error('Error starting development server:', error);
  process.exit(1);
}
