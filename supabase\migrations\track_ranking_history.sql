-- Create the track_ranking_history table
CREATE TABLE IF NOT EXISTS track_ranking_history (
  id SERIAL PRIMARY KEY,
  spotify_id TEXT NOT NULL,
  track_id TEXT NOT NULL,
  position INTEGER NOT NULL,
  time_range TEXT NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (spotify_id) REFERENCES spotify_users(spotify_id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_track_history_spotify_id ON track_ranking_history(spotify_id);
CREATE INDEX IF NOT EXISTS idx_track_history_track_id ON track_ranking_history(track_id);
CREATE INDEX IF NOT EXISTS idx_track_history_track_user ON track_ranking_history(spotify_id, track_id);
