
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import TopTracks from "./pages/TopTracks";
import TopArtists from "./pages/TopArtists";
import Genres from "./pages/Genres";
import RecentPlays from "./pages/RecentPlays";
import Playlists from "./pages/Playlists";
import ListeningReport from "./pages/ListeningReport";
import NotFound from "./pages/NotFound";
import { SpotifyAuthProvider } from "./contexts/SpotifyAuthContext";
import ProtectedRoute from "./components/ProtectedRoute";

// Create a new QueryClient instance
const queryClient = new QueryClient();

const App = () => (
  <BrowserRouter>
    <QueryClientProvider client={queryClient}>
      <SpotifyAuthProvider>
        <Toaster />
        <Sonner />
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/top-tracks" element={<ProtectedRoute><TopTracks /></ProtectedRoute>} />
          <Route path="/top-artists" element={<ProtectedRoute><TopArtists /></ProtectedRoute>} />
          <Route path="/genres" element={<ProtectedRoute><Genres /></ProtectedRoute>} />
          <Route path="/recent" element={<ProtectedRoute><RecentPlays /></ProtectedRoute>} />
          <Route path="/playlists" element={<ProtectedRoute><Playlists /></ProtectedRoute>} />
          <Route path="/listening-report" element={<ProtectedRoute><ListeningReport /></ProtectedRoute>} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </SpotifyAuthProvider>
    </QueryClientProvider>
  </BrowserRouter>
);

export default App;
