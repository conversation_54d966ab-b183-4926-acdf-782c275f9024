
import React, { useState, useEffect } from 'react';
import SpotifyLayout from '../components/SpotifyLayout';
import TimeRangeSelector from '../components/TimeRangeSelector';
import { Clock, Music } from 'lucide-react';
import { useTopTracks } from '@/hooks/use-spotify-data';
import { TimeRange, SpotifyService, SpotifyTrack } from '@/services/spotify';
import SongRankingModal from '@/components/SongRankingModal';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { toast } from '@/components/ui/sonner';
import PositionChangeIndicator, { PositionChangeType } from '@/components/PositionChangeIndicator';

interface TrackPositionData {
  [trackId: string]: number;
}

interface TrackPositionChange {
  [trackId: string]: PositionChangeType;
}

const TopTracks = () => {
  const [timeRange, setTimeRange] = useState<TimeRange>('medium_term');
  const [selectedTrack, setSelectedTrack] = useState<SpotifyTrack | null>(null);
  const [selectedPosition, setSelectedPosition] = useState<number>(0);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [previousPositions, setPreviousPositions] = useState<TrackPositionData>({});
  const [positionChanges, setPositionChanges] = useState<TrackPositionChange>({});
  const { user } = useSpotifyAuth();

  // Fetch top tracks using the custom hook
  const { data: topTracksData, isLoading } = useTopTracks(timeRange, 50);

  // Fetch previous positions and calculate position changes
  useEffect(() => {
    const fetchPreviousPositions = async () => {
      if (!topTracksData?.items || !user?.id) return;

      try {
        // Create a map of current positions
        const currentPositions: TrackPositionData = {};
        topTracksData.items.forEach((track, index) => {
          currentPositions[track.id] = index + 1;
        });

        // Fetch previous positions for each track
        const trackPromises = topTracksData.items.map(async (track) => {
          try {
            const response = await fetch(
              `/.netlify/functions/track-ranking-history?spotify_id=${user.id}&track_id=${track.id}&time_range=${timeRange}`
            );

            if (!response.ok) {
              throw new Error('Failed to fetch track history');
            }

            const data = await response.json();

            // If there's history data, get the previous position (second to last entry)
            if (data.history && data.history.length > 1) {
              // Get the second to last entry (previous position)
              const previousEntry = data.history[data.history.length - 2];
              return { trackId: track.id, previousPosition: previousEntry.position };
            } else if (data.history && data.history.length === 1) {
              // If there's only one entry, it's the current position, so this is a new entry
              return { trackId: track.id, previousPosition: null };
            }

            // If no history, it's a new entry
            return { trackId: track.id, previousPosition: null };
          } catch (error) {
            console.error(`Error fetching history for track ${track.id}:`, error);
            return { trackId: track.id, previousPosition: null };
          }
        });

        // Wait for all promises to resolve
        const results = await Promise.all(trackPromises);

        // Build the previous positions map
        const prevPositions: TrackPositionData = {};
        const changes: TrackPositionChange = {};

        results.forEach(({ trackId, previousPosition }) => {
          if (previousPosition === null) {
            // New entry
            changes[trackId] = 'new';
          } else {
            prevPositions[trackId] = previousPosition;

            // Calculate position change
            const currentPos = currentPositions[trackId];
            if (currentPos < previousPosition) {
              changes[trackId] = 'up'; // Moved up (lower number is better)
            } else if (currentPos > previousPosition) {
              changes[trackId] = 'down'; // Moved down
            } else {
              changes[trackId] = 'same'; // Same position
            }
          }
        });

        setPreviousPositions(prevPositions);
        setPositionChanges(changes);
      } catch (error) {
        console.error('Error fetching previous positions:', error);
      }
    };

    fetchPreviousPositions();
  }, [topTracksData, timeRange, user]);

  // Store track ranking data when tracks are loaded
  useEffect(() => {
    const storeTrackRankings = async () => {
      if (!topTracksData?.items || !user?.id) return;

      try {
        await fetch('/.netlify/functions/track-ranking-history', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tracks: topTracksData.items,
            time_range: timeRange,
            spotify_id: user.id,
          }),
        });
      } catch (error) {
        console.error('Error storing track rankings:', error);
        // Don't show an error toast to the user as this is a background operation
      }
    };

    storeTrackRankings();
  }, [topTracksData, timeRange, user]);

  const handleTrackClick = (track: SpotifyTrack, position: number) => {
    setSelectedTrack(track);
    setSelectedPosition(position);
    setIsModalOpen(true);
  };

  return (
    <SpotifyLayout>
      <div>
        <h1 className="text-3xl font-bold mb-6">top tracks</h1>
        <TimeRangeSelector selected={timeRange} onChange={setTimeRange} />

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
          </div>
        ) : (
          <div className="rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="border-b border-spotify-light-gray">
                <tr className="text-left text-spotify-off-white text-sm">
                  <th className="px-4 py-2 w-8">#</th>
                  <th className="px-4 py-2">title</th>
                  <th className="px-4 py-2 hidden md:table-cell">album</th>
                  <th className="px-4 py-2 text-right">
                    <Clock size={16} />
                  </th>
                </tr>
              </thead>
              <tbody>
                {topTracksData?.items.map((track, index) => (
                  <tr
                    key={track.id}
                    className="hover:bg-spotify-light-gray group cursor-pointer"
                    onClick={() => handleTrackClick(track, index + 1)}
                  >
                    <td className="px-4 py-3 text-spotify-off-white">
                      <div className="flex items-center">
                        <PositionChangeIndicator
                          changeType={positionChanges[track.id] || 'new'}
                          changeAmount={
                            positionChanges[track.id] === 'up' || positionChanges[track.id] === 'down'
                              ? Math.abs((index + 1) - (previousPositions[track.id] || 0))
                              : 0
                          }
                        />
                        <div className="w-4 text-center">{index + 1}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        <img
                          src={track.album.images[0]?.url || ''}
                          alt={track.name}
                          className="w-10 h-10 object-cover"
                        />
                        <div>
                          <div className="font-medium">{track.name}</div>
                          <div className="text-sm text-spotify-off-white">
                            {track.artists.map(artist => artist.name).join(', ')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-spotify-off-white hidden md:table-cell">{track.album.name}</td>
                    <td className="px-4 py-3 text-spotify-off-white text-right">
                      {SpotifyService.formatDuration(track.duration_ms)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Song Ranking Modal */}
        <SongRankingModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          track={selectedTrack}
          currentPosition={selectedPosition}
          timeRange={timeRange}
        />
      </div>
    </SpotifyLayout>
  );
};

export default TopTracks;
