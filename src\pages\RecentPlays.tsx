
import React from 'react';
import SpotifyLayout from '../components/SpotifyLayout';
import { Clock } from 'lucide-react';
import { useRecentlyPlayed } from '@/hooks/use-spotify-data';
import { SpotifyService } from '@/services/spotify';

const RecentPlays = () => {
  // Fetch recently played tracks using the custom hook
  const { data: recentlyPlayedData, isLoading } = useRecentlyPlayed(50);

  return (
    <SpotifyLayout>
      <div>
        <h1 className="text-3xl font-bold mb-6">recently played</h1>

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
          </div>
        ) : (
          <div className="rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="border-b border-spotify-light-gray">
                <tr className="text-left text-spotify-off-white text-sm">
                  <th className="px-4 py-2 w-8"></th>
                  <th className="px-4 py-2">Title</th>
                  <th className="px-4 py-2 hidden md:table-cell">Album</th>
                  <th className="px-4 py-2">Played At</th>
                  <th className="px-4 py-2 text-right">
                    <Clock size={16} />
                  </th>
                </tr>
              </thead>
              <tbody>
                {recentlyPlayedData?.items.map((item) => (
                  <tr key={item.track.id + item.played_at} className="hover:bg-spotify-light-gray group">
                    <td className="px-4 py-3 text-spotify-off-white">
                      {/* Play button removed */}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-3">
                        <img
                          src={item.track.album.images[0]?.url || ''}
                          alt={item.track.name}
                          className="w-10 h-10 object-cover"
                        />
                        <div>
                          <div className="font-medium">{item.track.name}</div>
                          <div className="text-sm text-spotify-off-white">
                            {item.track.artists.map(artist => artist.name).join(', ')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-spotify-off-white hidden md:table-cell">{item.track.album.name}</td>
                    <td className="px-4 py-3 text-spotify-off-white">{SpotifyService.formatDate(item.played_at)}</td>
                    <td className="px-4 py-3 text-spotify-off-white text-right">
                      {SpotifyService.formatDuration(item.track.duration_ms)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </SpotifyLayout>
  );
};

export default RecentPlays;
