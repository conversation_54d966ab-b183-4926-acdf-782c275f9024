// Netlify build plugin to handle environment variables
module.exports = {
  onPreBuild: ({ utils }) => {
    // Check for required environment variables
    const requiredEnvVars = [
      'SPOTIFY_CLIENT_ID',
      'SPOTIFY_CLIENT_SECRET',
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY'
    ];

    let missingEnvVars = [];
    
    requiredEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        missingEnvVars.push(envVar);
      }
    });

    if (missingEnvVars.length > 0) {
      return utils.build.failBuild(`Missing required environment variables: ${missingEnvVars.join(', ')}. Please set these in your Netlify dashboard.`);
    }

    // Set client-side environment variables if they're not already set
    if (!process.env.VITE_SUPABASE_URL && process.env.SUPABASE_URL) {
      process.env.VITE_SUPABASE_URL = process.env.SUPABASE_URL;
      console.log('Set VITE_SUPABASE_URL from SUPABASE_URL');
    }

    if (!process.env.VITE_SUPABASE_ANON_KEY && process.env.SUPABASE_ANON_KEY) {
      process.env.VITE_SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
      console.log('Set VITE_SUPABASE_ANON_KEY from SUPABASE_ANON_KEY');
    }

    // Set the Spotify redirect URI for production if not already set
    if (!process.env.SPOTIFY_REDIRECT_URI && process.env.URL) {
      process.env.SPOTIFY_REDIRECT_URI = `${process.env.URL}/.netlify/functions/callback`;
      console.log(`Set SPOTIFY_REDIRECT_URI to ${process.env.SPOTIFY_REDIRECT_URI}`);
    }

    console.log('Environment variables validated successfully.');
  }
};
