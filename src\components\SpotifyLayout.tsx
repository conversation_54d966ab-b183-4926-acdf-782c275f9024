
import React from 'react';
import SpotifySidebar from './SpotifySidebar';
import SpotifyHeader from './SpotifyHeader';
import { SidebarProvider } from '@/components/ui/sidebar';

interface SpotifyLayoutProps {
  children: React.ReactNode;
}

const SpotifyLayout: React.FC<SpotifyLayoutProps> = ({ children }) => {
  return (
    <SidebarProvider>
      <div className="flex h-screen overflow-hidden bg-spotify-black">
        <SpotifySidebar />
        <main className="flex-1 flex flex-col overflow-hidden">
          <SpotifyHeader />
          <div className="flex-1 overflow-y-auto p-6">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default SpotifyLayout;
