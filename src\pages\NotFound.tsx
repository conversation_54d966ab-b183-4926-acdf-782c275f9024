
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import { Home } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-spotify-black text-white">
      <div className="text-center max-w-md px-4">
        <h1 className="text-6xl font-bold mb-4">404</h1>
        <p className="text-2xl mb-6">Page not found</p>
        <p className="text-spotify-off-white mb-8">
          we couldn't find the page you were looking for. it might have been removed or doesn't exist.
        </p>
        <Link
          to="/"
          className="inline-flex items-center gap-2 bg-spotify-green text-black font-bold py-3 px-8 rounded-full hover:scale-105 transition-transform"
        >
          <Home size={18} />
          back to home
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
