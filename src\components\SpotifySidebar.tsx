
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Music, BarChart3, Clock, Library, Tag, ListMusic } from 'lucide-react';
import { Sidebar, useSidebar } from '@/components/ui/sidebar';
import PlaylistCreator from './PlaylistCreator';
import { useUserPlaylists } from '@/hooks/use-spotify-data';

const SpotifySidebar: React.FC = () => {
  const { isMobile } = useSidebar();
  const location = useLocation();
  const { data: userPlaylists, isLoading: isLoadingPlaylists } = useUserPlaylists();

  // Function to determine if a link is active
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const sidebarContent = (
    <>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-white flex items-center gap-2">
          <span className="text-spotify-green">musilize</span>
        </h1>
      </div>

      <div className="flex-1 overflow-y-auto">
        <nav className="px-3">
          <ul className="space-y-2">
            <li>
              <Link
                to="/"
                className={`flex items-center gap-4 px-3 py-2 rounded transition-colors duration-200 ${isActive('/')
                  ? 'sidebar-link-active'
                  : 'text-spotify-off-white hover:text-spotify-green spotify-hover'
                  }`}
              >
                <Home size={24} />
                <span className="font-medium">home</span>
              </Link>
            </li>
            <li>
              <Link
                to="/top-tracks"
                className={`flex items-center gap-4 px-3 py-2 rounded transition-colors duration-200 ${isActive('/top-tracks')
                  ? 'sidebar-link-active'
                  : 'text-spotify-off-white hover:text-spotify-green spotify-hover'
                  }`}
              >
                <Music size={24} />
                <span className="font-medium">top tracks</span>
              </Link>
            </li>
            <li>
              <Link
                to="/top-artists"
                className={`flex items-center gap-4 px-3 py-2 rounded transition-colors duration-200 ${isActive('/top-artists')
                  ? 'sidebar-link-active'
                  : 'text-spotify-off-white hover:text-spotify-green spotify-hover'
                  }`}
              >
                <BarChart3 size={24} />
                <span className="font-medium">top artists</span>
              </Link>
            </li>
            <li>
              <Link
                to="/genres"
                className={`flex items-center gap-4 px-3 py-2 rounded transition-colors duration-200 ${isActive('/genres')
                  ? 'sidebar-link-active'
                  : 'text-spotify-off-white hover:text-spotify-green spotify-hover'
                  }`}
              >
                <Tag size={24} />
                <span className="font-medium">genres</span>
              </Link>
            </li>
            <li>
              <Link
                to="/recent"
                className={`flex items-center gap-4 px-3 py-2 rounded transition-colors duration-200 ${isActive('/recent')
                  ? 'sidebar-link-active'
                  : 'text-spotify-off-white hover:text-spotify-green spotify-hover'
                  }`}
              >
                <Clock size={24} />
                <span className="font-medium">recent plays</span>
              </Link>
            </li>
            <li>
              <Link
                to="/playlists"
                className={`flex items-center gap-4 px-3 py-2 rounded transition-colors duration-200 ${isActive('/playlists')
                  ? 'sidebar-link-active'
                  : 'text-spotify-off-white hover:text-spotify-green spotify-hover'
                  }`}
              >
                <ListMusic size={24} />
                <span className="font-medium">playlists</span>
              </Link>
            </li>
          </ul>
        </nav>

        <div className="px-6 mt-6">
          <h2 className="uppercase text-xs font-bold tracking-wider text-spotify-off-white mb-4">
            your library
          </h2>

          {userPlaylists && userPlaylists.length > 0 ? (
            <div className="space-y-2">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">your playlists</span>
                <PlaylistCreator buttonText="+ new" className="text-xs bg-transparent text-spotify-green border border-spotify-green rounded-full px-2 py-0.5 hover:bg-spotify-green/10 whitespace-nowrap" />
              </div>

              {userPlaylists.map(playlist => (
                <a
                  key={playlist.id}
                  href={playlist.external_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 p-2 rounded-md hover:bg-spotify-light-gray transition-all duration-200 text-spotify-off-white hover:text-white group"
                >
                  <div className="bg-spotify-green/10 p-1.5 rounded-full group-hover:bg-spotify-green/20 transition-colors">
                    <ListMusic size={14} className="text-spotify-green" />
                  </div>
                  <div className="overflow-hidden">
                    <div className="text-sm truncate group-hover:text-spotify-green transition-colors">{playlist.name}</div>
                    <div className="flex items-center gap-1.5 text-xs text-spotify-off-white truncate">
                      <span className={`inline-block h-1.5 w-1.5 rounded-full ${playlist.time_range === 'short_term' ? 'bg-blue-400' :
                        playlist.time_range === 'medium_term' ? 'bg-purple-400' : 'bg-red-400'
                        }`}></span>
                      {playlist.track_count} tracks
                    </div>
                  </div>
                </a>
              ))}
            </div>
          ) : (
            <div className="bg-spotify-light-gray rounded-md p-4">
              <div className="font-medium mb-1">create your first playlist</div>
              <div className="text-sm text-spotify-off-white mb-4">it's easy, we'll help you</div>
              <PlaylistCreator />
            </div>
          )}
        </div>
      </div>
    </>
  );

  return (
    <Sidebar
      className="bg-black text-white"
      collapsible="offcanvas"
    >
      <div className="flex flex-col h-full w-full">
        {sidebarContent}
      </div>
    </Sidebar>
  );
};

export default SpotifySidebar;
