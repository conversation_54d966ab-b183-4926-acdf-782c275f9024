import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import SpotifyLayout from '../components/SpotifyLayout';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { useTopTracks, useTopArtists } from '@/hooks/use-spotify-data';
import { TimeRange, SpotifyTrack, SpotifyArtist } from '@/services/spotify';
import { ChevronDown, ChevronUp, Music, BarChart3, Clock, Award, Headphones, RefreshCw, AlertTriangle } from 'lucide-react';
import { ChartContainer } from '@/components/ui/chart';
import * as RechartsPrimitive from 'recharts';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/sonner';
import { useIsMobile } from '@/hooks/use-mobile';

const ListeningReport = () => {
  const { user, tokens } = useSpotifyAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [activeSection, setActiveSection] = useState<string | null>(isMobile ? null : 'listeningStats');
  const [retryCount, setRetryCount] = useState(0);

  // Fetch top tracks and artists for different time ranges
  const { data: topTracksShortTerm } = useTopTracks('short_term', 10);
  const { data: topTracksMediumTerm } = useTopTracks('medium_term', 10);
  const { data: topTracksLongTerm } = useTopTracks('long_term', 10);
  const { data: topArtistsShortTerm } = useTopArtists('short_term', 10);
  const { data: topArtistsMediumTerm } = useTopArtists('medium_term', 10);
  const { data: topArtistsLongTerm } = useTopArtists('long_term', 10);

  useEffect(() => {
    const fetchReportData = async () => {
      if (!tokens?.accessToken || !user?.id) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch aggregated report data from our Netlify function
        const response = await fetch(
          `/.netlify/functions/listening-report?access_token=${tokens.accessToken}&spotify_id=${user.id}`
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error('Error response:', response.status, errorData);
          throw new Error(errorData.message || `Failed to fetch listening report data (${response.status})`);
        }

        const data = await response.json();
        setReportData(data);
      } catch (error) {
        console.error('Error fetching listening report:', error);
        setError(error instanceof Error ? error.message : 'Failed to load your listening report');
        toast.error('Failed to load your listening report. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchReportData();
  }, [tokens, user, retryCount]);

  // Toggle section visibility
  const toggleSection = (section: string) => {
    if (activeSection === section) {
      setActiveSection(null);
    } else {
      setActiveSection(section);
    }
  };

  // Helper to get genre counts from artists
  const getGenreCounts = (artists: SpotifyArtist[] = []) => {
    const genreCounts: Record<string, number> = {};

    artists.forEach(artist => {
      artist.genres.forEach(genre => {
        genreCounts[genre] = (genreCounts[genre] || 0) + 1;
      });
    });

    return Object.entries(genreCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([name, count]) => ({ name, count }));
  };

  // Prepare genre data for charts
  const topGenresShortTerm = topArtistsShortTerm ? getGenreCounts(topArtistsShortTerm.items) : [];
  const topGenresMediumTerm = topArtistsMediumTerm ? getGenreCounts(topArtistsMediumTerm.items) : [];
  const topGenresLongTerm = topArtistsLongTerm ? getGenreCounts(topArtistsLongTerm.items) : [];

  // Function to retry loading the report
  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  return (
    <SpotifyLayout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-4">your listening report</h1>
          <p className="text-spotify-off-white">
            discover insights about your music taste and listening habits over time
          </p>
        </div>

        {loading ? (
          <div className="space-y-8">
            <Skeleton className="h-64 w-full rounded-lg" />
            <Skeleton className="h-64 w-full rounded-lg" />
            <Skeleton className="h-64 w-full rounded-lg" />
          </div>
        ) : error ? (
          <div className="bg-spotify-light-gray/20 rounded-lg p-8 text-center space-y-4">
            <div className="flex justify-center">
              <AlertTriangle size={48} className="text-yellow-500" />
            </div>
            <h2 className="text-2xl font-bold">Oops! Something went wrong</h2>
            <p className="text-spotify-off-white max-w-md mx-auto">
              {error}
            </p>
            <div className="flex justify-center gap-4 mt-6">
              <Button
                onClick={handleRetry}
                className="bg-spotify-green text-black hover:bg-spotify-green/90 py-3 px-8 rounded-full flex items-center gap-2"
              >
                <RefreshCw size={16} />
                retry
              </Button>
              <Button
                onClick={() => navigate('/')}
                className="bg-spotify-dark-gray text-white hover:bg-spotify-light-gray py-3 px-8 rounded-full"
              >
                back to home
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Top Tracks Section */}
            <div className="bg-spotify-light-gray/20 rounded-lg overflow-hidden">
              <div
                className="p-6 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('topTracks')}
              >
                <div className="flex items-center gap-3">
                  <div className="bg-spotify-green/20 p-2 rounded-full">
                    <Music size={24} className="text-spotify-green" />
                  </div>
                  <h2 className="text-2xl font-bold">top tracks</h2>
                </div>
                {activeSection === 'topTracks' ? (
                  <ChevronUp size={24} />
                ) : (
                  <ChevronDown size={24} />
                )}
              </div>

              {activeSection === 'topTracks' && (
                <div className="p-6 pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">last month</h3>
                      <ul className="space-y-3">
                        {topTracksShortTerm?.items.slice(0, 5).map((track, index) => (
                          <li key={track.id} className="flex items-center gap-3">
                            <div className="bg-spotify-green/10 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1 truncate">
                              <div className="font-medium truncate">{track.name}</div>
                              <div className="text-xs text-spotify-off-white truncate">
                                {track.artists[0].name}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">last 6 months</h3>
                      <ul className="space-y-3">
                        {topTracksMediumTerm?.items.slice(0, 5).map((track, index) => (
                          <li key={track.id} className="flex items-center gap-3">
                            <div className="bg-spotify-green/10 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1 truncate">
                              <div className="font-medium truncate">{track.name}</div>
                              <div className="text-xs text-spotify-off-white truncate">
                                {track.artists[0].name}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">all time</h3>
                      <ul className="space-y-3">
                        {topTracksLongTerm?.items.slice(0, 5).map((track, index) => (
                          <li key={track.id} className="flex items-center gap-3">
                            <div className="bg-spotify-green/10 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1 truncate">
                              <div className="font-medium truncate">{track.name}</div>
                              <div className="text-xs text-spotify-off-white truncate">
                                {track.artists[0].name}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Top Artists Section */}
            <div className="bg-spotify-light-gray/20 rounded-lg overflow-hidden">
              <div
                className="p-6 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('topArtists')}
              >
                <div className="flex items-center gap-3">
                  <div className="bg-spotify-green/20 p-2 rounded-full">
                    <BarChart3 size={24} className="text-spotify-green" />
                  </div>
                  <h2 className="text-2xl font-bold">top artists</h2>
                </div>
                {activeSection === 'topArtists' ? (
                  <ChevronUp size={24} />
                ) : (
                  <ChevronDown size={24} />
                )}
              </div>

              {activeSection === 'topArtists' && (
                <div className="p-6 pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">last month</h3>
                      <ul className="space-y-3">
                        {topArtistsShortTerm?.items.slice(0, 5).map((artist, index) => (
                          <li key={artist.id} className="flex items-center gap-3">
                            <div className="bg-spotify-green/10 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium truncate">{artist.name}</div>
                              <div className="text-xs text-spotify-off-white truncate">
                                {artist.genres.slice(0, 2).join(', ')}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">last 6 months</h3>
                      <ul className="space-y-3">
                        {topArtistsMediumTerm?.items.slice(0, 5).map((artist, index) => (
                          <li key={artist.id} className="flex items-center gap-3">
                            <div className="bg-spotify-green/10 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium truncate">{artist.name}</div>
                              <div className="text-xs text-spotify-off-white truncate">
                                {artist.genres.slice(0, 2).join(', ')}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">all time</h3>
                      <ul className="space-y-3">
                        {topArtistsLongTerm?.items.slice(0, 5).map((artist, index) => (
                          <li key={artist.id} className="flex items-center gap-3">
                            <div className="bg-spotify-green/10 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium truncate">{artist.name}</div>
                              <div className="text-xs text-spotify-off-white truncate">
                                {artist.genres.slice(0, 2).join(', ')}
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Top Genres Section */}
            <div className="bg-spotify-light-gray/20 rounded-lg overflow-hidden">
              <div
                className="p-6 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('topGenres')}
              >
                <div className="flex items-center gap-3">
                  <div className="bg-spotify-green/20 p-2 rounded-full">
                    <Headphones size={24} className="text-spotify-green" />
                  </div>
                  <h2 className="text-2xl font-bold">top genres</h2>
                </div>
                {activeSection === 'topGenres' ? (
                  <ChevronUp size={24} />
                ) : (
                  <ChevronDown size={24} />
                )}
              </div>

              {activeSection === 'topGenres' && (
                <div className="p-6 pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">last month</h3>
                      {topGenresShortTerm.length > 0 ? (
                        <div className="h-64">
                          <ChartContainer
                            config={{
                              bar: { color: "#1DB954" },
                            }}
                            className="h-full w-full"
                          >
                            <RechartsPrimitive.ResponsiveContainer width="100%" height="100%">
                              <RechartsPrimitive.BarChart
                                data={topGenresShortTerm}
                                layout="vertical"
                                margin={{
                                  top: 10,
                                  right: 10,
                                  bottom: 10,
                                  left: isMobile ? 60 : 80
                                }}
                              >
                                <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" stroke="#333" />
                                <RechartsPrimitive.XAxis type="number" stroke="#999" />
                                <RechartsPrimitive.YAxis
                                  dataKey="name"
                                  type="category"
                                  stroke="#999"
                                  tick={{
                                    fill: '#999',
                                    fontSize: isMobile ? 10 : 12
                                  }}
                                  width={isMobile ? 60 : 80}
                                />
                                <RechartsPrimitive.Tooltip
                                  content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                      return (
                                        <div className="bg-spotify-dark-gray p-2 rounded border border-spotify-light-gray">
                                          <p className="text-white">{payload[0].payload.name}</p>
                                          <p className="text-spotify-off-white text-xs">
                                            {payload[0].value} artists
                                          </p>
                                        </div>
                                      );
                                    }
                                    return null;
                                  }}
                                />
                                <RechartsPrimitive.Bar dataKey="count" fill="#1DB954" radius={[0, 4, 4, 0]} />
                              </RechartsPrimitive.BarChart>
                            </RechartsPrimitive.ResponsiveContainer>
                          </ChartContainer>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-64 text-spotify-off-white">
                          No genre data available
                        </div>
                      )}
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">last 6 months</h3>
                      {topGenresMediumTerm.length > 0 ? (
                        <div className="h-64">
                          <ChartContainer
                            config={{
                              bar: { color: "#1DB954" },
                            }}
                            className="h-full w-full"
                          >
                            <RechartsPrimitive.ResponsiveContainer width="100%" height="100%">
                              <RechartsPrimitive.BarChart
                                data={topGenresMediumTerm}
                                layout="vertical"
                                margin={{
                                  top: 10,
                                  right: 10,
                                  bottom: 10,
                                  left: isMobile ? 60 : 80
                                }}
                              >
                                <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" stroke="#333" />
                                <RechartsPrimitive.XAxis type="number" stroke="#999" />
                                <RechartsPrimitive.YAxis
                                  dataKey="name"
                                  type="category"
                                  stroke="#999"
                                  tick={{
                                    fill: '#999',
                                    fontSize: isMobile ? 10 : 12
                                  }}
                                  width={isMobile ? 60 : 80}
                                />
                                <RechartsPrimitive.Tooltip
                                  content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                      return (
                                        <div className="bg-spotify-dark-gray p-2 rounded border border-spotify-light-gray">
                                          <p className="text-white">{payload[0].payload.name}</p>
                                          <p className="text-spotify-off-white text-xs">
                                            {payload[0].value} artists
                                          </p>
                                        </div>
                                      );
                                    }
                                    return null;
                                  }}
                                />
                                <RechartsPrimitive.Bar dataKey="count" fill="#1DB954" radius={[0, 4, 4, 0]} />
                              </RechartsPrimitive.BarChart>
                            </RechartsPrimitive.ResponsiveContainer>
                          </ChartContainer>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-64 text-spotify-off-white">
                          No genre data available
                        </div>
                      )}
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg">
                      <h3 className="text-lg font-bold mb-4 text-center">all time</h3>
                      {topGenresLongTerm.length > 0 ? (
                        <div className="h-64">
                          <ChartContainer
                            config={{
                              bar: { color: "#1DB954" },
                            }}
                            className="h-full w-full"
                          >
                            <RechartsPrimitive.ResponsiveContainer width="100%" height="100%">
                              <RechartsPrimitive.BarChart
                                data={topGenresLongTerm}
                                layout="vertical"
                                margin={{
                                  top: 10,
                                  right: 10,
                                  bottom: 10,
                                  left: isMobile ? 60 : 80
                                }}
                              >
                                <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" stroke="#333" />
                                <RechartsPrimitive.XAxis type="number" stroke="#999" />
                                <RechartsPrimitive.YAxis
                                  dataKey="name"
                                  type="category"
                                  stroke="#999"
                                  tick={{
                                    fill: '#999',
                                    fontSize: isMobile ? 10 : 12
                                  }}
                                  width={isMobile ? 60 : 80}
                                />
                                <RechartsPrimitive.Tooltip
                                  content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                      return (
                                        <div className="bg-spotify-dark-gray p-2 rounded border border-spotify-light-gray">
                                          <p className="text-white">{payload[0].payload.name}</p>
                                          <p className="text-spotify-off-white text-xs">
                                            {payload[0].value} artists
                                          </p>
                                        </div>
                                      );
                                    }
                                    return null;
                                  }}
                                />
                                <RechartsPrimitive.Bar dataKey="count" fill="#1DB954" radius={[0, 4, 4, 0]} />
                              </RechartsPrimitive.BarChart>
                            </RechartsPrimitive.ResponsiveContainer>
                          </ChartContainer>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-64 text-spotify-off-white">
                          No genre data available
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Listening Stats Section */}
            <div className="bg-spotify-light-gray/20 rounded-lg overflow-hidden">
              <div
                className="p-6 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('listeningStats')}
              >
                <div className="flex items-center gap-3">
                  <div className="bg-spotify-green/20 p-2 rounded-full">
                    <Award size={24} className="text-spotify-green" />
                  </div>
                  <h2 className="text-2xl font-bold">listening stats</h2>
                </div>
                {activeSection === 'listeningStats' ? (
                  <ChevronUp size={24} />
                ) : (
                  <ChevronDown size={24} />
                )}
              </div>

              {activeSection === 'listeningStats' && (
                <div className="p-6 pt-0">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg flex flex-col items-center justify-center text-center min-h-[100px]">
                      <div className={`font-bold text-spotify-green mb-2 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
                        {reportData?.totalArtists || '150+'}
                      </div>
                      <div className="text-sm text-spotify-off-white">unique artists explored</div>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg flex flex-col items-center justify-center text-center min-h-[100px]">
                      <div className={`font-bold text-spotify-green mb-2 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
                        {reportData?.totalGenres || '42'}
                      </div>
                      <div className="text-sm text-spotify-off-white">different genres</div>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg flex flex-col items-center justify-center text-center min-h-[100px]">
                      <div className={`font-bold text-spotify-green mb-2 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
                        {reportData?.minutesListened?.toLocaleString() || '12,543'}
                      </div>
                      <div className="text-sm text-spotify-off-white">minutes listened</div>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg flex flex-col items-center justify-center text-center min-h-[100px]">
                      <div className={`font-bold text-spotify-green mb-2 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
                        {reportData?.topDecade || '2010s'}
                      </div>
                      <div className="text-sm text-spotify-off-white">favorite decade</div>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg flex flex-col items-center justify-center text-center min-h-[100px]">
                      <div className={`font-bold text-spotify-green mb-2 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
                        {reportData?.energyLevel || 'high'}
                      </div>
                      <div className="text-sm text-spotify-off-white">energy level</div>
                    </div>

                    <div className="bg-spotify-dark-gray/40 p-4 rounded-lg flex flex-col items-center justify-center text-center min-h-[100px]">
                      <div className={`font-bold text-spotify-green mb-2 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>
                        {reportData?.moodScore || 'upbeat'}
                      </div>
                      <div className="text-sm text-spotify-off-white">mood score</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-center mt-8">
              <Button
                onClick={() => navigate('/')}
                className="bg-spotify-green text-black hover:bg-spotify-green/90 py-3 px-8 rounded-full w-full sm:w-auto"
              >
                back to home
              </Button>
            </div>
          </div>
        )}
      </div>
    </SpotifyLayout>
  );
};

export default ListeningReport;
