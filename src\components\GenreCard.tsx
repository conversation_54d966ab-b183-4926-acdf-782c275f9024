import React from 'react';
import { Music } from 'lucide-react';

interface GenreCardProps {
  name: string;
  count: number;
  rank?: number;
}

const GenreCard: React.FC<GenreCardProps> = ({ name, count, rank }) => {
  return (
    <div className="spotify-card flex flex-col items-center text-center group">
      <div className="relative w-full aspect-square bg-spotify-light-gray rounded-md mb-4 flex items-center justify-center">
        <div className="bg-spotify-green/20 rounded-full p-6">
          <Music size={40} className="text-spotify-green" />
        </div>
        {rank && (
          <div className="absolute top-2 right-2 bg-black/60 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
            {rank}
          </div>
        )}
      </div>
      <div className="font-medium w-full overflow-hidden text-ellipsis" title={name}>{name}</div>
      <div className="text-xs text-spotify-off-white">{count} artists</div>
    </div>
  );
};

export default GenreCard;
