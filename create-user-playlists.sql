-- Create the user_playlists table
CREATE TABLE IF NOT EXISTS user_playlists (
  id SERIAL PRIMARY KEY,
  spotify_id TEXT NOT NULL,
  playlist_id TEXT UNIQUE NOT NULL,
  playlist_name TEXT NOT NULL,
  time_range TEXT NOT NULL,
  track_count INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on spotify_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_playlists_spotify_id ON user_playlists(spotify_id);
