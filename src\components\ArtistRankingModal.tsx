import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { SpotifyArtist, TimeRange } from '@/services/spotify';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import * as RechartsPrimitive from 'recharts';
import { ChartContainer } from '@/components/ui/chart';
import { format } from 'date-fns';
import { toast } from '@/components/ui/sonner';

interface ArtistRankingModalProps {
  isOpen: boolean;
  onClose: () => void;
  artist: SpotifyArtist | null;
  currentPosition: number;
  timeRange: TimeRange;
}

interface RankingHistoryEntry {
  position: number;
  recorded_at: string;
}

interface ChartDataPoint {
  date: string;
  position: number;
  formattedDate: string;
}

const ArtistRankingModal: React.FC<ArtistRankingModalProps> = ({
  isOpen,
  onClose,
  artist,
  currentPosition,
  timeRange
}) => {
  const [historyData, setHistoryData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState(false);
  const [highestPosition, setHighestPosition] = useState<number | null>(null);
  const { user } = useSpotifyAuth();

  useEffect(() => {
    if (!isOpen || !artist || !user?.id) {
      return;
    }

    const fetchRankingHistory = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `/.netlify/functions/artist-ranking-history?spotify_id=${user.id}&artist_id=${artist.id}&time_range=${timeRange}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch artist ranking history');
        }

        const data = await response.json();
        setHighestPosition(data.highestPosition);

        // If there's no history, create a single data point for the current position
        if (!data.history || data.history.length === 0) {
          const now = new Date().toISOString();
          setHistoryData([{
            date: now,
            position: currentPosition,
            formattedDate: format(new Date(now), 'MMM d')
          }]);
        } else {
          // Format the data for the chart
          const chartData = data.history.map((entry: RankingHistoryEntry) => ({
            date: entry.recorded_at,
            position: entry.position,
            formattedDate: format(new Date(entry.recorded_at), 'MMM d')
          }));

          // Add current position if it's not already the latest entry
          const latestEntry = chartData[chartData.length - 1];
          const now = new Date();
          const latestDate = new Date(latestEntry.date);
          const timeDiff = now.getTime() - latestDate.getTime();
          const hoursDiff = timeDiff / (1000 * 60 * 60);

          // If the latest entry is more than 1 hour old, add current position
          if (hoursDiff > 1) {
            chartData.push({
              date: now.toISOString(),
              position: currentPosition,
              formattedDate: format(now, 'MMM d')
            });
          }

          setHistoryData(chartData);
        }
      } catch (error) {
        console.error('Error fetching artist ranking history:', error);
        toast.error('Failed to load ranking history');
        // Create a fallback data point
        const now = new Date().toISOString();
        setHistoryData([{
          date: now,
          position: currentPosition,
          formattedDate: format(new Date(now), 'MMM d')
        }]);
      } finally {
        setLoading(false);
      }
    };

    fetchRankingHistory();
  }, [isOpen, artist, user?.id, timeRange, currentPosition]);

  if (!artist) return null;

  const timeRangeLabels = {
    short_term: 'last month',
    medium_term: 'last 6 months',
    long_term: 'all time'
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-2xl w-[95%]">
        <DialogHeader>
          <DialogTitle className="text-center">
            {artist.name} - ranking progression
          </DialogTitle>
          <DialogDescription className="text-center text-spotify-off-white text-sm">
            {timeRangeLabels[timeRange]} • current position: #{currentPosition}
            {highestPosition && (
              <span className="text-spotify-green"> • highest: #{highestPosition}</span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-center">
            <img
              src={artist.images[0]?.url || '/placeholder-artist.png'}
              alt={artist.name}
              className="w-20 h-20 rounded-full object-cover"
            />
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-t-spotify-green"></div>
            </div>
          ) : (
            <>
              <div className="h-64 w-full flex justify-center items-center">
                <div className="w-full" style={{ marginLeft: "-15px" }}>
                  <ChartContainer
                    config={{
                      line: { color: "#1DB954" }, // Spotify green
                      dot: { color: "#1DB954" },
                      currentDot: { color: "#1DB954" },
                    }}
                    className="h-full w-full"
                  >
                    <RechartsPrimitive.LineChart
                      data={historyData}
                      margin={{ top: 20, right: 0, bottom: 20, left: 0 }}
                      className="mx-auto"
                    >
                      <RechartsPrimitive.CartesianGrid strokeDasharray="3 3" stroke="#333" />
                      <RechartsPrimitive.XAxis
                        dataKey="formattedDate"
                        stroke="#999"
                        tick={{ fill: '#999' }}
                      />
                      <RechartsPrimitive.YAxis
                        stroke="#999"
                        tick={{ fill: '#999' }}
                        domain={['dataMin', 'dataMax']}
                        reversed={true} // Higher position numbers are lower on the chart
                        width={30}
                      />
                      <RechartsPrimitive.Tooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="bg-spotify-dark-gray p-2 rounded border border-spotify-light-gray">
                                <p className="text-white">{`Position: ${payload[0].value}`}</p>
                                <p className="text-spotify-off-white text-xs">{payload[0].payload.formattedDate}</p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <RechartsPrimitive.Line
                        type="monotone"
                        dataKey="position"
                        stroke="#1DB954"
                        strokeWidth={2}
                        dot={{ r: 4, fill: "#1DB954" }}
                        activeDot={{ r: 6, fill: "#1DB954" }}
                      />
                      {/* Blinking dot for current position */}
                      {historyData.length > 0 && (
                        <RechartsPrimitive.Scatter
                          data={[historyData[historyData.length - 1]]}
                          dataKey="position"
                          fill="#1DB954"
                          className="animate-pulse-spotify"
                        >
                          <RechartsPrimitive.Cell r={6} />
                        </RechartsPrimitive.Scatter>
                      )}
                    </RechartsPrimitive.LineChart>
                  </ChartContainer>
                </div>
              </div>

              <div className="text-center text-sm text-spotify-off-white">
                <p>
                  {historyData.length > 1
                    ? `Tracking ${historyData.length} data points over time`
                    : 'Start of tracking - more data will appear as you use the app'
                  }
                </p>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ArtistRankingModal;
