
import React from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  titleTooltip?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, titleTooltip }) => {
  return (
    <div className="spotify-card flex items-center gap-4">
      <div className="bg-spotify-green/20 p-3 rounded-full">
        {icon}
      </div>
      <div>
        <h3 className="text-sm text-spotify-off-white">{title}</h3>
        <p className="text-xl font-bold overflow-hidden text-ellipsis" title={titleTooltip || String(value)}>{value}</p>
      </div>
    </div>
  );
};

export default StatCard;
