-- Create the genre_ranking_history table
CREATE TABLE IF NOT EXISTS genre_ranking_history (
  id SERIAL PRIMARY KEY,
  spotify_id TEXT NOT NULL,
  genre_name TEXT NOT NULL,
  count INTEGER NOT NULL,
  position INTEGER NOT NULL,
  time_range TEXT NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (spotify_id) REFERENCES spotify_users(spotify_id) ON DELETE CASCADE
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_genre_history_spotify_id ON genre_ranking_history(spotify_id);
CREATE INDEX IF NOT EXISTS idx_genre_history_genre_name ON genre_ranking_history(genre_name);
CREATE INDEX IF NOT EXISTS idx_genre_history_genre_user ON genre_ranking_history(spotify_id, genre_name);
CREATE INDEX IF NOT EXISTS idx_genre_history_time_range ON genre_ranking_history(time_range);
CREATE INDEX IF NOT EXISTS idx_genre_history_recorded_at ON genre_ranking_history(recorded_at);
