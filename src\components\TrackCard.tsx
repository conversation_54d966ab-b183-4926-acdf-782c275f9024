
import React from 'react';

interface TrackCardProps {
  title: string;
  artist: string;
  imageUrl: string;
  rank?: number;
}

const TrackCard: React.FC<TrackCardProps> = ({ title, artist, imageUrl, rank }) => {
  return (
    <div className="spotify-card group relative">
      <div className="relative">
        <img
          src={imageUrl}
          alt={`${title} by ${artist}`}
          className="w-full aspect-square object-cover rounded-md shadow-lg mb-4"
        />
        <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity">
          {/* Play button removed, keeping only the shading effect */}
        </div>
        {rank && (
          <div className="absolute top-2 left-2 bg-black/70 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
            {rank}
          </div>
        )}
      </div>
      <h3 className="font-bold text-base truncate">{title}</h3>
      <p className="text-spotify-off-white text-sm truncate">{artist}</p>
    </div>
  );
};

export default TrackCard;
