const { defineConfig, loadEnv } = require("vite");
const react = require("@vitejs/plugin-react-swc");
const path = require("path");
const dotenv = require("dotenv");

// Load environment variables from .env file
dotenv.config();

// https://vitejs.dev/config/
module.exports = defineConfig(({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), '');

  // Ensure client-side environment variables are set
  if (env.SUPABASE_URL && !env.VITE_SUPABASE_URL) {
    env.VITE_SUPABASE_URL = env.SUPABASE_URL;
    console.log('Set VITE_SUPABASE_URL from SUPABASE_URL');
  }

  if (env.SUPABASE_ANON_KEY && !env.VITE_SUPABASE_ANON_KEY) {
    env.VITE_SUPABASE_ANON_KEY = env.SUPABASE_ANON_KEY;
    console.log('Set VITE_SUPABASE_ANON_KEY from SUPABASE_ANON_KEY');
  }

  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [
      react(),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // Define environment variables for client-side
    define: {
      'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL || env.SUPABASE_URL || ''),
      'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY || env.SUPABASE_ANON_KEY || ''),
    }
  };
});
