const spotifyApi = require('./utils/spotify');
const { createClient } = require('@supabase/supabase-js');
const { supabaseConfig } = require('./config');

// Initialize Supabase client
const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey);

exports.handler = async function (event, context) {
  console.log('Create playlist function called');

  try {
    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
      console.log('Method not allowed:', event.httpMethod);
      return {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Parse the request body
    const body = JSON.parse(event.body);
    console.log('Request body:', JSON.stringify(body, null, 2));

    const { access_token, time_range, limit = 50, spotify_id, playlist_name } = body;

    // Validate required parameters
    if (!access_token) {
      console.error('Missing access_token');
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Missing access_token' })
      };
    }

    if (!time_range) {
      console.error('Missing time_range');
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Missing time_range' })
      };
    }

    if (!spotify_id) {
      console.error('Missing spotify_id');
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Missing spotify_id' })
      };
    }

    console.log('Validated parameters:', {
      access_token_length: access_token.length,
      time_range,
      spotify_id,
      has_playlist_name: !!playlist_name
    });

    if (!access_token) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Access token is required' })
      };
    }

    if (!time_range) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Time range is required' })
      };
    }

    if (!spotify_id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Spotify ID is required' })
      };
    }

    // Set the access token
    console.log('Setting access token and fetching top tracks');
    try {
      spotifyApi.setAccessToken(access_token);
      console.log('Access token set successfully');
    } catch (setTokenError) {
      console.error('Error setting access token:', setTokenError);
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Failed to set access token', details: setTokenError.message })
      };
    }

    // Get the user's top tracks for the specified time range
    let topTracksData;
    try {
      console.log(`Fetching top tracks for time range: ${time_range}, limit: ${limit}`);

      // Verify the Spotify API instance is properly configured
      console.log('Spotify API configuration:', {
        hasAccessToken: !!spotifyApi.getAccessToken(),
        hasCredentials: !!(spotifyApi.getClientId() && spotifyApi.getClientSecret()),
        redirectUri: spotifyApi.getRedirectURI()
      });

      topTracksData = await spotifyApi.getMyTopTracks({
        time_range: time_range,
        limit: limit
      });

      console.log(`Fetched ${topTracksData.body.items?.length || 0} tracks`);

      if (!topTracksData.body.items || topTracksData.body.items.length === 0) {
        console.log('No tracks found for the specified time range');
        return {
          statusCode: 404,
          body: JSON.stringify({ error: 'No tracks found for the specified time range' })
        };
      }
    } catch (trackError) {
      console.error('Error fetching top tracks:', trackError);
      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to fetch top tracks',
          details: trackError.message,
          stack: trackError.stack
        })
      };
    }

    // Generate a default playlist name if not provided
    const timeRangeText = time_range === 'short_term' ? 'last month' :
      time_range === 'medium_term' ? 'last 6 months' : 'all time';
    const defaultPlaylistName = `musilize: top tracks (${timeRangeText})`;
    const finalPlaylistName = playlist_name || defaultPlaylistName;

    // Create a new playlist
    let playlistData;
    try {
      console.log(`Creating playlist "${finalPlaylistName}" for user ${spotify_id}`);

      // Verify user ID format
      if (!spotify_id.match(/^[0-9a-zA-Z]+$/)) {
        console.error('Invalid Spotify user ID format:', spotify_id);
        return {
          statusCode: 400,
          body: JSON.stringify({ error: 'Invalid Spotify user ID format' })
        };
      }

      // Double check that we have a valid access token
      const currentToken = spotifyApi.getAccessToken();
      if (!currentToken) {
        console.error('Access token not set or lost before creating playlist');
        return {
          statusCode: 401,
          body: JSON.stringify({ error: 'Access token not available for playlist creation' })
        };
      }

      console.log('Creating playlist with parameters:', {
        userId: spotify_id,
        name: finalPlaylistName,
        description: `your top tracks from ${timeRangeText}, created by musilize.`,
        public: false
      });

      // Implement retry logic for playlist creation
      const maxRetries = 3;
      let retryCount = 0;
      let lastError = null;

      while (retryCount < maxRetries) {
        try {
          console.log(`Attempt ${retryCount + 1} of ${maxRetries} to create playlist`);
          console.log('Using Spotify API with parameters:', {
            userId: spotify_id,
            name: finalPlaylistName,
            description: `your top tracks from ${timeRangeText}, created by musilize.`,
            public: false
          });

          // Log the current state of the Spotify API
          console.log('Spotify API state:', {
            hasAccessToken: !!spotifyApi.getAccessToken(),
            accessTokenLength: spotifyApi.getAccessToken() ? spotifyApi.getAccessToken().length : 0,
            hasCredentials: !!(spotifyApi.getClientId() && spotifyApi.getClientSecret()),
            redirectUri: spotifyApi.getRedirectURI()
          });

          // Create the playlist using the library
          try {
            // Add a unique identifier to the description
            const uniqueIdentifier = "musilize-app-playlist";
            playlistData = await spotifyApi.createPlaylist(spotify_id, finalPlaylistName, {
              description: `your top tracks from ${timeRangeText}, created by musilize. [${uniqueIdentifier}]`,
              public: false
            });

            console.log('Playlist creation successful using library');
          } catch (libraryError) {
            console.error('Error using Spotify library:', libraryError);
            console.log('Attempting direct API call as fallback...');

            // If the library call fails, try a direct API call as fallback
            const fetch = require('node-fetch');
            const response = await fetch(`https://api.spotify.com/v1/users/${spotify_id}/playlists`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${access_token}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                name: finalPlaylistName,
                description: `your top tracks from ${timeRangeText}, created by musilize. [musilize-app-playlist]`,
                public: false
              })
            });

            if (!response.ok) {
              const errorText = await response.text();
              console.error(`Direct API call failed with status ${response.status}:`, errorText);
              throw new Error(`Direct API call failed: ${response.status} ${errorText}`);
            }

            const data = await response.json();
            console.log('Direct API call successful:', data);

            // Format the response to match the library format
            playlistData = {
              body: data
            };
          }

          // If successful, break out of the retry loop
          console.log('Playlist creation successful');
          break;
        } catch (retryError) {
          lastError = retryError;
          retryCount++;

          // Log the retry attempt
          console.error(`Playlist creation attempt ${retryCount} failed:`, retryError.message);

          // Log more details about the error
          if (retryError.response) {
            console.error('Error response:', retryError.response);
          }

          if (retryError.body) {
            console.error('Error body:', retryError.body);
          }

          // If we've reached max retries, throw the last error
          if (retryCount >= maxRetries) {
            console.error(`Max retries (${maxRetries}) reached. Giving up.`);
            throw lastError;
          }

          // Wait before retrying (exponential backoff)
          const waitTime = Math.min(1000 * Math.pow(2, retryCount), 10000);
          console.log(`Waiting ${waitTime}ms before retry ${retryCount + 1}...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }

      if (!playlistData) {
        console.error('No playlist data returned from Spotify API');
        return {
          statusCode: 500,
          body: JSON.stringify({ error: 'No playlist data returned from Spotify API' })
        };
      }

      console.log('Raw playlist data:', JSON.stringify(playlistData, null, 2));

      // Handle different response formats
      if (!playlistData.body) {
        console.error('Playlist data missing body property:', playlistData);

        // Check if the playlist data is directly in the response
        if (playlistData.id && playlistData.name && playlistData.external_urls) {
          console.log('Found playlist data in root of response');
          // Create a compatible structure
          playlistData = {
            body: playlistData
          };
        } else {
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: 'Playlist created but response format is invalid',
              data: playlistData
            })
          };
        }
      }

      if (!playlistData.body.id) {
        console.error('Playlist data missing ID:', playlistData.body);

        // Try to extract ID from other properties
        if (playlistData.body.uri) {
          const uriParts = playlistData.body.uri.split(':');
          if (uriParts.length === 3 && uriParts[0] === 'spotify' && uriParts[1] === 'playlist') {
            console.log('Extracted playlist ID from URI:', uriParts[2]);
            playlistData.body.id = uriParts[2];
          }
        } else if (playlistData.body.href) {
          const hrefParts = playlistData.body.href.split('/');
          if (hrefParts.length > 0) {
            const potentialId = hrefParts[hrefParts.length - 1];
            console.log('Extracted playlist ID from href:', potentialId);
            playlistData.body.id = potentialId;
          }
        } else {
          return {
            statusCode: 500,
            body: JSON.stringify({
              error: 'Playlist created but missing ID in response',
              data: playlistData.body
            })
          };
        }
      }

      console.log(`Playlist created with ID: ${playlistData.body.id}`);
    } catch (playlistError) {
      console.error('Error creating playlist:', playlistError);
      console.error('Error details:', playlistError.message);
      console.error('Error stack:', playlistError.stack);

      // Log the error body if available
      if (playlistError.body) {
        console.error('Error response body:', JSON.stringify(playlistError.body, null, 2));
      }

      // Log the status code if available
      if (playlistError.statusCode) {
        console.error('Error status code:', playlistError.statusCode);
      }

      // Check for specific error types
      if (playlistError.message.includes('No such user')) {
        return {
          statusCode: 404,
          body: JSON.stringify({ error: 'Spotify user not found', details: playlistError.message })
        };
      }

      if (playlistError.message.includes('access token')) {
        return {
          statusCode: 401,
          body: JSON.stringify({ error: 'Invalid or expired access token', details: playlistError.message })
        };
      }

      // Check for permission errors
      if (playlistError.message.includes('permission') ||
        (playlistError.body && playlistError.body.error &&
          playlistError.body.error.message &&
          playlistError.body.error.message.includes('permission'))) {
        return {
          statusCode: 403,
          body: JSON.stringify({
            error: 'Permission denied',
            details: 'The application does not have permission to create playlists. Make sure you have granted the necessary permissions during login.',
            originalError: playlistError.message
          })
        };
      }

      // Check for rate limiting
      if (playlistError.statusCode === 429 ||
        (playlistError.body && playlistError.body.error && playlistError.body.error.status === 429)) {
        return {
          statusCode: 429,
          body: JSON.stringify({
            error: 'Rate limit exceeded',
            details: 'Too many requests to Spotify API. Please try again later.',
            originalError: playlistError.message
          })
        };
      }

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Failed to create playlist',
          details: playlistError.message,
          stack: playlistError.stack,
          body: playlistError.body || null
        })
      };
    }

    // Extract track URIs
    const trackUris = topTracksData.body.items.map(track => track.uri);
    console.log(`Adding ${trackUris.length} tracks to playlist`);

    // Add tracks to the playlist
    try {
      console.log(`Adding ${trackUris.length} tracks to playlist ${playlistData.body.id}`);

      // Check if we have tracks to add
      if (!trackUris.length) {
        console.error('No track URIs to add to playlist');
        return {
          statusCode: 400,
          body: JSON.stringify({ error: 'No tracks to add to playlist' })
        };
      }

      // Add tracks in batches of 100 (Spotify API limit)
      const batchSize = 100;
      for (let i = 0; i < trackUris.length; i += batchSize) {
        const batch = trackUris.slice(i, i + batchSize);
        console.log(`Adding batch of ${batch.length} tracks (${i + 1} to ${i + batch.length})`);
        await spotifyApi.addTracksToPlaylist(playlistData.body.id, batch);
      }

      console.log('All tracks added to playlist successfully');
    } catch (addTracksError) {
      console.error('Error adding tracks to playlist:', addTracksError);
      console.error('Error details:', addTracksError.message);
      console.error('Error stack:', addTracksError.stack);

      // The playlist was created, but we couldn't add tracks
      // Return a partial success response
      return {
        statusCode: 207, // Partial success
        body: JSON.stringify({
          warning: 'Playlist was created but tracks could not be added',
          error: 'Failed to add tracks to playlist',
          details: addTracksError.message,
          playlist: {
            id: playlistData.body.id,
            name: playlistData.body.name,
            external_url: playlistData.body.external_urls.spotify,
            track_count: 0,
            time_range: time_range
          }
        })
      };
    }

    // Try to store the playlist in Supabase
    try {
      // First check if the table exists
      const { data: tableExists, error: tableCheckError } = await supabase
        .from('user_playlists')
        .select('count(*)', { count: 'exact', head: true })
        .limit(0);

      if (tableCheckError) {
        console.error('Error checking if user_playlists table exists:', tableCheckError);
        // If table doesn't exist, we'll create it
        if (tableCheckError.code === '42P01') { // PostgreSQL code for undefined_table
          console.log('Table does not exist, creating it...');
          await supabase.rpc('exec_sql', {
            sql: `
              CREATE TABLE IF NOT EXISTS user_playlists (
                id SERIAL PRIMARY KEY,
                spotify_id TEXT NOT NULL,
                playlist_id TEXT UNIQUE NOT NULL,
                playlist_name TEXT NOT NULL,
                time_range TEXT NOT NULL,
                track_count INTEGER NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
              CREATE INDEX IF NOT EXISTS idx_user_playlists_spotify_id ON user_playlists(spotify_id);
            `
          });
        }
      }

      // Now insert the playlist data
      const currentTime = new Date().toISOString();
      console.log(`Storing playlist in database with time_range: ${time_range}`);
      const { error } = await supabase
        .from('user_playlists')
        .upsert({
          spotify_id: spotify_id,
          playlist_id: playlistData.body.id,
          playlist_name: finalPlaylistName,
          time_range: time_range,
          track_count: trackUris.length,
          updated_at: currentTime,
          last_updated_at: currentTime
        });

      if (error) {
        console.error('Error storing playlist in Supabase:', error);
      }
    } catch (dbError) {
      console.error('Database operation failed:', dbError);
      // Continue anyway since the playlist was created in Spotify
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        playlist: {
          id: playlistData.body.id,
          name: playlistData.body.name,
          external_url: playlistData.body.external_urls.spotify,
          track_count: trackUris.length,
          time_range: time_range
        }
      })
    };
  } catch (error) {
    console.error('Error creating playlist:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to create playlist', details: error.message })
    };
  }
};
