#!/usr/bin/env node
// @ts-check

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupRankingTables() {
  console.log('Setting up ranking tables in Supabase...');

  try {
    // Create track_ranking_history table
    console.log('Creating track_ranking_history table...');
    const { error: trackError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS track_ranking_history (
          id SERIAL PRIMARY KEY,
          spotify_id TEXT NOT NULL,
          track_id TEXT NOT NULL,
          position INTEGER NOT NULL,
          time_range TEXT NOT NULL,
          recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_track_history_spotify_id ON track_ranking_history(spotify_id);
        CREATE INDEX IF NOT EXISTS idx_track_history_track_id ON track_ranking_history(track_id);
        CREATE INDEX IF NOT EXISTS idx_track_history_track_user ON track_ranking_history(spotify_id, track_id);
        CREATE INDEX IF NOT EXISTS idx_track_history_time_range ON track_ranking_history(time_range);
        CREATE INDEX IF NOT EXISTS idx_track_history_recorded_at ON track_ranking_history(recorded_at);
      `
    });

    if (trackError) {
      console.error('Error creating track_ranking_history table:', trackError);
    } else {
      console.log('track_ranking_history table created successfully!');
    }

    // Create artist_ranking_history table
    console.log('Creating artist_ranking_history table...');
    const { error: artistError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS artist_ranking_history (
          id SERIAL PRIMARY KEY,
          spotify_id TEXT NOT NULL,
          artist_id TEXT NOT NULL,
          position INTEGER NOT NULL,
          time_range TEXT NOT NULL,
          recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_artist_history_spotify_id ON artist_ranking_history(spotify_id);
        CREATE INDEX IF NOT EXISTS idx_artist_history_artist_id ON artist_ranking_history(artist_id);
        CREATE INDEX IF NOT EXISTS idx_artist_history_artist_user ON artist_ranking_history(spotify_id, artist_id);
        CREATE INDEX IF NOT EXISTS idx_artist_history_time_range ON artist_ranking_history(time_range);
        CREATE INDEX IF NOT EXISTS idx_artist_history_recorded_at ON artist_ranking_history(recorded_at);
      `
    });

    if (artistError) {
      console.error('Error creating artist_ranking_history table:', artistError);
    } else {
      console.log('artist_ranking_history table created successfully!');
    }

    // Create genre_ranking_history table
    console.log('Creating genre_ranking_history table...');
    const { error: genreError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS genre_ranking_history (
          id SERIAL PRIMARY KEY,
          spotify_id TEXT NOT NULL,
          genre_name TEXT NOT NULL,
          count INTEGER NOT NULL,
          position INTEGER NOT NULL,
          time_range TEXT NOT NULL,
          recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS idx_genre_history_spotify_id ON genre_ranking_history(spotify_id);
        CREATE INDEX IF NOT EXISTS idx_genre_history_genre_name ON genre_ranking_history(genre_name);
        CREATE INDEX IF NOT EXISTS idx_genre_history_genre_user ON genre_ranking_history(spotify_id, genre_name);
        CREATE INDEX IF NOT EXISTS idx_genre_history_time_range ON genre_ranking_history(time_range);
        CREATE INDEX IF NOT EXISTS idx_genre_history_recorded_at ON genre_ranking_history(recorded_at);
      `
    });

    if (genreError) {
      console.error('Error creating genre_ranking_history table:', genreError);
    } else {
      console.log('genre_ranking_history table created successfully!');
    }

    console.log('All ranking tables setup completed!');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the setup
setupRankingTables();
