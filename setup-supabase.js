#!/usr/bin/env node
// @ts-check

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function setupSpotifyUsersTable() {
  console.log('Setting up spotify_users table in Supabase...');

  try {
    // Check if the table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'spotify_users');

    if (tablesError) {
      throw tablesError;
    }

    // If the table doesn't exist, create it
    if (!tables || tables.length === 0) {
      console.log('Creating spotify_users table...');

      // Create the table
      const { error: createError } = await supabase.rpc('exec_sql', {
        query: `
          CREATE TABLE IF NOT EXISTS spotify_users (
            id SERIAL PRIMARY KEY,
            spotify_id TEXT UNIQUE NOT NULL,
            display_name TEXT,
            email TEXT,
            profile_image TEXT,
            access_token TEXT NOT NULL,
            refresh_token TEXT NOT NULL,
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create an index on spotify_id for faster lookups
          CREATE INDEX IF NOT EXISTS idx_spotify_users_spotify_id ON spotify_users(spotify_id);

          -- Create a function to update the updated_at timestamp
          CREATE OR REPLACE FUNCTION update_updated_at_column()
          RETURNS TRIGGER AS $$
          BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
          END;
          $$ LANGUAGE plpgsql;

          -- Create a trigger to automatically update the updated_at column
          DROP TRIGGER IF EXISTS update_spotify_users_updated_at ON spotify_users;
          CREATE TRIGGER update_spotify_users_updated_at
          BEFORE UPDATE ON spotify_users
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
        `
      });

      if (createError) {
        throw createError;
      }

      console.log('spotify_users table created successfully!');
    } else {
      console.log('spotify_users table already exists.');
    }

    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Error setting up Supabase:', error);
  }
}

setupSpotifyUsersTable();
