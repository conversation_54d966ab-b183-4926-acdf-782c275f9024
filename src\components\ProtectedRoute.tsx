
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useSpotifyAuth();

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center bg-spotify-black">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/" />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
