
import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import SpotifyLayout from '../components/SpotifyLayout';
import TrackCard from '../components/TrackCard';
import ArtistCard from '../components/ArtistCard';
import GenreCard from '../components/GenreCard';
import StatCard from '../components/StatCard';
import ArtistTopTracksModal from '../components/ArtistTopTracksModal';
import {
  BarChart3,
  Clock,
  Music,
  Headphones,
  Volume2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Chart,
  ListMusic,
  Calendar,
  History,
  Award,
  TrendingUp,
  Sparkles,
  HelpCircle
} from 'lucide-react';
import { useTopTracks, useTopArtists } from '@/hooks/use-spotify-data';
import { TimeRange, SpotifyArtist } from '@/services/spotify';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const Index = () => {
  const { user, loading, loginWithSpotify } = useSpotifyAuth();
  const [timeRange, setTimeRange] = useState<TimeRange>('medium_term');
  const [selectedArtist, setSelectedArtist] = useState<SpotifyArtist | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  // Fetch top tracks and artists using custom hooks
  const { data: topTracksData, isLoading: isLoadingTracks } = useTopTracks(timeRange, 5);
  const { data: topArtistsData, isLoading: isLoadingArtists } = useTopArtists(timeRange, 20);

  const handleArtistClick = (artist: SpotifyArtist) => {
    try {
      console.log('Artist clicked on Index page:', {
        id: artist?.id || 'missing-id',
        name: artist?.name || 'missing-name'
      });

      // Create a sanitized copy of the artist data with fallbacks for all properties
      const sanitizedArtist: SpotifyArtist = {
        id: artist?.id || `unknown-${Date.now()}`,
        name: artist?.name || 'Unknown Artist',
        images: Array.isArray(artist?.images) ? artist.images : [],
        genres: Array.isArray(artist?.genres) ? artist.genres : [],
        popularity: typeof artist?.popularity === 'number' ? artist.popularity : 0,
        external_urls: artist?.external_urls || { spotify: '' }
      };

      setSelectedArtist(sanitizedArtist);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error handling artist click:', error);
      // Show a fallback artist if there's an error
      setSelectedArtist({
        id: 'error',
        name: 'Error Loading Artist',
        images: [],
        genres: [],
        popularity: 0,
        external_urls: { spotify: '' }
      });
      setIsModalOpen(true);
    }
  };

  // Process genres from artists data
  const genreCounts = useMemo(() => {
    if (!topArtistsData?.items) return [];

    const genreMap = new Map<string, { name: string, count: number }>();

    // Count occurrences of each genre
    topArtistsData.items.forEach(artist => {
      artist.genres.forEach(genre => {
        if (!genreMap.has(genre)) {
          genreMap.set(genre, { name: genre, count: 0 });
        }

        const genreData = genreMap.get(genre)!;
        genreData.count += 1;
      });
    });

    // Convert map to array and sort by count (descending)
    return Array.from(genreMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Take top 5 genres for the home page
  }, [topArtistsData]);

  return (
    <SpotifyLayout>
      {loading ? (
        <div className="flex h-full items-center justify-center">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
        </div>
      ) : !user ? (
        <div className="flex flex-col items-center justify-center py-8">
          {/* Hero Section */}
          <div className="w-full max-w-5xl mb-12">
            <div className="text-center mb-8">
              <h1 className="text-5xl font-bold text-white mb-4">welcome to musilize</h1>
              <p className="text-xl text-spotify-off-white max-w-2xl mx-auto">
                your personal Spotify insights dashboard that reveals your unique music journey
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-8 items-center justify-center mb-12">
              <div className="w-full md:w-1/2 bg-spotify-dark-gray rounded-lg p-6 shadow-xl border border-spotify-light-gray/30">
                <div className="text-center mb-6">
                  <Music size={48} className="mx-auto text-spotify-green mb-4" />
                  <h2 className="text-2xl font-bold text-white mb-2">connect with Spotify</h2>
                  <p className="text-spotify-off-white">
                    login with your Spotify account to unlock your personalized music insights
                  </p>
                </div>

                <Button
                  onClick={loginWithSpotify}
                  className="w-full bg-spotify-green text-black hover:bg-spotify-green/90 py-6 text-lg font-bold"
                >
                  <span className="flex items-center justify-center">
                    <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z" />
                    </svg>
                    Login with Spotify
                  </span>
                </Button>

                <div className="mt-6 text-center">
                  <p className="text-xs text-spotify-off-white">
                    By continuing, you agree to Spotify's Terms of Service and Privacy Policy.
                  </p>
                </div>
              </div>

              <div className="w-full md:w-1/2 bg-gradient-to-br from-spotify-dark-gray to-black rounded-lg p-6 shadow-xl border border-spotify-light-gray/30">
                <h3 className="text-xl font-bold text-white mb-4">what you'll discover:</h3>
                <ul className="space-y-4">
                  <li className="flex items-start gap-3">
                    <BarChart3 className="text-spotify-green mt-1 flex-shrink-0" />
                    <div>
                      <span className="font-bold text-white">top tracks & artists</span>
                      <p className="text-spotify-off-white text-sm">see what you've been listening to most over different time periods</p>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <PieChart className="text-spotify-green mt-1 flex-shrink-0" />
                    <div>
                      <span className="font-bold text-white">genre breakdowns</span>
                      <p className="text-spotify-off-white text-sm">discover your music taste across different genres</p>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <LineChart className="text-spotify-green mt-1 flex-shrink-0" />
                    <div>
                      <span className="font-bold text-white">listening trends</span>
                      <p className="text-spotify-off-white text-sm">track how your music preferences evolve over time</p>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <ListMusic className="text-spotify-green mt-1 flex-shrink-0" />
                    <div>
                      <span className="font-bold text-white">auto-updating playlists</span>
                      <p className="text-spotify-off-white text-sm">create playlists that refresh with your latest favorites</p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Features Section */}
          <div className="w-full max-w-5xl mb-12">
            <h2 className="text-3xl font-bold text-white text-center mb-8">features</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-spotify-dark-gray rounded-lg p-6 hover:bg-spotify-light-gray/80 transition-colors">
                <Award className="text-spotify-green mb-4 h-10 w-10" />
                <h3 className="text-xl font-bold text-white mb-2">top tracks</h3>
                <p className="text-spotify-off-white">discover your most played songs across different time periods</p>
              </div>
              <div className="bg-spotify-dark-gray rounded-lg p-6 hover:bg-spotify-light-gray/80 transition-colors">
                <Headphones className="text-spotify-green mb-4 h-10 w-10" />
                <h3 className="text-xl font-bold text-white mb-2">artist insights</h3>
                <p className="text-spotify-off-white">see which artists dominate your listening habits</p>
              </div>
              <div className="bg-spotify-dark-gray rounded-lg p-6 hover:bg-spotify-light-gray/80 transition-colors">
                <BarChart className="text-spotify-green mb-4 h-10 w-10" />
                <h3 className="text-xl font-bold text-white mb-2">genre analysis</h3>
                <p className="text-spotify-off-white">visualize your music taste across different genres</p>
              </div>
              <div className="bg-spotify-dark-gray rounded-lg p-6 hover:bg-spotify-light-gray/80 transition-colors">
                <History className="text-spotify-green mb-4 h-10 w-10" />
                <h3 className="text-xl font-bold text-white mb-2">listening history</h3>
                <p className="text-spotify-off-white">review your recently played tracks with timestamps</p>
              </div>
              <div className="bg-spotify-dark-gray rounded-lg p-6 hover:bg-spotify-light-gray/80 transition-colors">
                <TrendingUp className="text-spotify-green mb-4 h-10 w-10" />
                <h3 className="text-xl font-bold text-white mb-2">trend tracking</h3>
                <p className="text-spotify-off-white">see how your music preferences change over time</p>
              </div>
              <div className="bg-spotify-dark-gray rounded-lg p-6 hover:bg-spotify-light-gray/80 transition-colors">
                <Sparkles className="text-spotify-green mb-4 h-10 w-10" />
                <h3 className="text-xl font-bold text-white mb-2">personalized report</h3>
                <p className="text-spotify-off-white">get a detailed analysis of your unique listening profile</p>
              </div>
            </div>
          </div>

          {/* How It Works Section */}
          <div className="w-full max-w-5xl mb-12">
            <h2 className="text-3xl font-bold text-white text-center mb-8">how it works</h2>
            <div className="flex flex-col md:flex-row gap-6 items-center">
              <div className="w-full md:w-1/3 bg-spotify-dark-gray rounded-lg p-6 text-center">
                <div className="bg-spotify-green/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-spotify-green font-bold text-xl">1</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">connect</h3>
                <p className="text-spotify-off-white">lgin with your Spotify account securely</p>
              </div>
              <div className="w-full md:w-1/3 bg-spotify-dark-gray rounded-lg p-6 text-center">
                <div className="bg-spotify-green/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-spotify-green font-bold text-xl">2</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">analyze</h3>
                <p className="text-spotify-off-white">we process your listening data from Spotify</p>
              </div>
              <div className="w-full md:w-1/3 bg-spotify-dark-gray rounded-lg p-6 text-center">
                <div className="bg-spotify-green/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                  <span className="text-spotify-green font-bold text-xl">3</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">discover</h3>
                <p className="text-spotify-off-white">explore your personalized music insights</p>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="w-full max-w-5xl mb-12">
            <h2 className="text-3xl font-bold text-white text-center mb-8">frequently asked questions</h2>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1" className="border-spotify-light-gray/30">
                <AccordionTrigger className="text-white hover:text-spotify-green">is musilize free to use?</AccordionTrigger>
                <AccordionContent className="text-spotify-off-white">
                  yes, musilize is completely free to use. all you need is a Spotify account to get started.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-2" className="border-spotify-light-gray/30">
                <AccordionTrigger className="text-white hover:text-spotify-green">is my Spotify data secure?</AccordionTrigger>
                <AccordionContent className="text-spotify-off-white">
                  we take data security seriously. we only access the data needed to provide insights, and we never share your personal information with third parties.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-3" className="border-spotify-light-gray/30">
                <AccordionTrigger className="text-white hover:text-spotify-green">how often is my data updated?</AccordionTrigger>
                <AccordionContent className="text-spotify-off-white">
                  your data is refreshed each time you log in, ensuring you always see the most up-to-date insights about your listening habits. you also have the option to manually update your playlists with a click of a button.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-4" className="border-spotify-light-gray/30">
                <AccordionTrigger className="text-white hover:text-spotify-green">do i need a Spotify Premium account?</AccordionTrigger>
                <AccordionContent className="text-spotify-off-white">
                  no, musilize works with both free and premium Spotify accounts. however, some features may be enhanced with a premium account.
                </AccordionContent>
              </AccordionItem>
              <AccordionItem value="item-5" className="border-spotify-light-gray/30">
                <AccordionTrigger className="text-white hover:text-spotify-green">can i create playlists with musilize?</AccordionTrigger>
                <AccordionContent className="text-spotify-off-white">
                  yes! you can create auto-updating playlists based on your top tracks that will refresh periodically to always include your latest favorites.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>

          {/* Final CTA */}
          <div className="w-full max-w-5xl">
            <div className="bg-gradient-to-r from-spotify-green/20 to-spotify-dark-gray/80 rounded-lg p-8 text-center">
              <h2 className="text-3xl font-bold text-white mb-4">ready to explore your music journey?</h2>
              <p className="text-spotify-off-white mb-6 max-w-2xl mx-auto">
                connect your Spotify account now to unlock personalized insights about your listening habits and discover more about your unique music taste.
              </p>
              <Button
                onClick={loginWithSpotify}
                className="bg-spotify-green text-black hover:bg-spotify-green/90 py-6 px-8 text-lg font-bold"
              >
                <span className="flex items-center justify-center">
                  <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z" />
                  </svg>
                  connect with Spotify
                </span>
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-8">
          <div>
            <h1 className="text-3xl font-bold mb-6">welcome to musilize</h1>
            <p className="text-spotify-off-white mb-6">
              discover insights about your music taste and listening habits.
              view your top tracks, artists, and detailed statistics based on your Spotify activity.
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <Link to="/genres">
                <StatCard
                  title="top genre"
                  value={topArtistsData?.items[0]?.genres[0] || "Loading..."}
                  titleTooltip={topArtistsData?.items[0]?.genres[0]}
                  icon={<Music size={24} className="text-spotify-green" />}
                />
              </Link>
              <StatCard title="minutes listened" value="12,543" icon={<Clock size={24} className="text-spotify-green" />} />
              <StatCard
                title="artists explored"
                value={topArtistsData ? `${topArtistsData.total}+` : "Loading..."}
                icon={<BarChart3 size={24} className="text-spotify-green" />}
              />
              <StatCard title="energy level" value="high" icon={<Volume2 size={24} className="text-spotify-green" />} />
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold">your top tracks</h2>
              <Link to="/top-tracks" className="text-spotify-off-white hover:text-spotify-green text-sm font-bold">
                see all
              </Link>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {isLoadingTracks ? (
                <div className="col-span-full flex justify-center py-8">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-t-spotify-green"></div>
                </div>
              ) : (
                topTracksData?.items.map((track, index) => (
                  <TrackCard
                    key={track.id}
                    title={track.name}
                    artist={track.artists[0].name}
                    imageUrl={track.album.images[0]?.url || ''}
                    rank={index + 1}
                  />
                ))
              )}
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold">your top artists</h2>
              <Link to="/top-artists" className="text-spotify-off-white hover:text-spotify-green text-sm font-bold">
                see all
              </Link>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {isLoadingArtists ? (
                <div className="col-span-full flex justify-center py-8">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-t-spotify-green"></div>
                </div>
              ) : (
                topArtistsData?.items.slice(0, 5).map((artist, index) => (
                  <ArtistCard
                    key={artist.id}
                    name={artist.name}
                    imageUrl={artist.images[0]?.url || ''}
                    rank={index + 1}
                    artist={artist}
                    onClick={handleArtistClick}
                  />
                ))
              )}
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold">your top genres</h2>
              <Link to="/genres" className="text-spotify-off-white hover:text-spotify-green text-sm font-bold">
                see all
              </Link>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {isLoadingArtists ? (
                <div className="col-span-full flex justify-center py-8">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-t-spotify-green"></div>
                </div>
              ) : (
                genreCounts.map((genre, index) => (
                  <GenreCard
                    key={genre.name}
                    name={genre.name}
                    count={genre.count}
                    rank={index + 1}
                  />
                ))
              )}
            </div>
          </div>

          <div className="bg-gradient-to-r from-spotify-light-gray/40 to-spotify-dark-gray/40 rounded-lg p-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div>
                <h2 className="text-2xl font-bold mb-2">listening report</h2>
                <p className="text-spotify-off-white">your personalized listening report is ready.</p>
              </div>

              <Link to="/listening-report">
                <button className="bg-spotify-green hover:bg-opacity-80 text-black font-bold py-3 px-8 rounded-full">
                  view report
                </button>
              </Link>
            </div>
          </div>

          {/* Artist Top Tracks Modal */}
          <ArtistTopTracksModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            artist={selectedArtist}
            timeRange={timeRange}
          />
        </div>
      )}
    </SpotifyLayout>
  );
};

export default Index;
