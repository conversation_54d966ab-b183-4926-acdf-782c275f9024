// This file is loaded by Netlify functions to ensure environment variables are properly set
require('dotenv').config();

// Log environment variable status (without revealing values)
console.log('Netlify function environment check:');
console.log('SPOTIFY_CLIENT_ID:', process.env.SPOTIFY_CLIENT_ID ? 'Set' : 'Not set');
console.log('SPOTIFY_CLIENT_SECRET:', process.env.SPOTIFY_CLIENT_SECRET ? 'Set' : 'Not set');
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? 'Set' : 'Not set');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'Set' : 'Not set');

// Export environment variables for use in other functions
module.exports = {
  env: process.env
};
