import React, { useState } from 'react';
import SpotifyLayout from '../components/SpotifyLayout';
import { useUserPlaylists, useUpdatePlaylist } from '@/hooks/use-spotify-data';
import { TimeRange, SpotifyPlaylist } from '@/services/spotify';
import { ListMusic, RefreshCw, ExternalLink, Loader2, LogIn } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PlaylistCreator from '@/components/PlaylistCreator';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';
import { toast } from '@/components/ui/sonner';


const Playlists = () => {
  const { data: userPlaylists, isLoading, refetch: refetchPlaylists } = useUserPlaylists();
  const playlists = userPlaylists as SpotifyPlaylist[] || [];
  const updatePlaylistMutation = useUpdatePlaylist();
  const { user, tokens, loginWithSpotify, loading } = useSpotifyAuth();
  const [isUpdatingAll, setIsUpdatingAll] = useState(false);

  // Refresh playlists when the component mounts or when user/tokens change
  React.useEffect(() => {
    if (user && tokens) {
      console.log('Playlists component mounted or user/tokens changed, refreshing playlists...');

      // Immediate refresh
      refetchPlaylists();

      // Set up an interval to periodically refresh playlists
      const intervalId = setInterval(() => {
        console.log('Periodic refresh of playlists...');
        refetchPlaylists();
      }, 10000); // Refresh every 10 seconds

      // Clean up the interval when the component unmounts
      return () => clearInterval(intervalId);
    }
  }, [user, tokens, refetchPlaylists]);

  const handleUpdatePlaylist = async (playlistId: string, timeRange: TimeRange) => {
    if (!user || !tokens) {
      toast.error('You must be logged in to update playlists');
      loginWithSpotify();
      return;
    }

    try {
      await updatePlaylistMutation.mutateAsync({
        playlistId,
        timeRange
      });
    } catch (error) {
      console.error('Error updating playlist:', error);
      toast.error(`Failed to update playlist: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Function to update all playlists
  const handleUpdateAllPlaylists = async () => {
    if (!user || !tokens) {
      toast.error('You must be logged in to update playlists');
      loginWithSpotify();
      return;
    }

    if (!playlists || playlists.length === 0) {
      toast.info('No playlists to update');
      return;
    }

    setIsUpdatingAll(true);
    toast.info(`Updating ${playlists.length} playlists with your latest top tracks...`, {
      duration: 5000,
      id: 'update-all-playlists'
    });

    try {
      // Update each playlist one by one
      for (const playlist of playlists) {
        try {
          toast.loading(`Updating "${playlist.name}"...`, {
            id: `update-${playlist.id}`,
            duration: 3000
          });

          await updatePlaylistMutation.mutateAsync({
            playlistId: playlist.id,
            timeRange: playlist.time_range as TimeRange
          });

          toast.success(`Updated "${playlist.name}" with your latest top tracks!`, {
            id: `update-${playlist.id}`,
            duration: 3000
          });

          // Small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (playlistError) {
          console.error(`Error updating playlist ${playlist.name}:`, playlistError);
          toast.error(`Failed to update "${playlist.name}". Try again later.`, {
            id: `update-${playlist.id}`,
            duration: 5000
          });
        }
      }

      // Refresh the playlists data
      await refetchPlaylists();

      toast.success(`Finished updating all playlists!`, {
        id: 'update-all-playlists',
        duration: 3000
      });
    } catch (error) {
      console.error('Error updating all playlists:', error);
      toast.error(`Failed to update all playlists: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUpdatingAll(false);
    }
  };

  const getTimeRangeText = (range: TimeRange) => {
    return range === 'short_term' ? 'last month' :
      range === 'medium_term' ? 'last 6 months' : 'all time';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <SpotifyLayout>
      <div>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">your playlists</h1>
          <div className="flex gap-2">
            {playlists && playlists.length > 0 && (
              <Button
                onClick={handleUpdateAllPlaylists}
                disabled={isUpdatingAll || updatePlaylistMutation.isPending}
                className="bg-spotify-light-gray text-white hover:bg-spotify-light-gray/90 font-bold py-2 px-4 rounded-full"
              >
                {isUpdatingAll ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    updating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    update All
                  </>
                )}
              </Button>
            )}
            <PlaylistCreator buttonText="create new playlist" className="bg-spotify-green text-black font-bold py-2 px-4 rounded-full hover:bg-spotify-green/90 whitespace-nowrap" />
          </div>
        </div>

        {!user || !tokens ? (
          <div className="bg-spotify-dark-gray rounded-lg p-8 text-center border border-spotify-light-gray/20"
            style={{ backgroundImage: "linear-gradient(to bottom right, rgba(40, 40, 40, 0.3), rgba(20, 20, 20, 0.5))" }}>
            <div className="bg-spotify-green/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
              <LogIn size={32} className="text-spotify-green" />
            </div>
            <h3 className="text-2xl font-bold mb-3">login required</h3>
            <p className="text-spotify-off-white mb-6 max-w-md mx-auto">you need to log in with your spotify account to view and manage your playlists</p>
            <Button
              onClick={loginWithSpotify}
              className="bg-spotify-green text-black hover:bg-spotify-green/90 font-bold py-2 px-6 rounded-full hover:scale-105 transition-all"
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <LogIn className="mr-2 h-4 w-4" />
              )}
              Log in with Spotify
            </Button>
          </div>
        ) : (
          <>
            <div className="mb-6">
              <p className="text-spotify-off-white mb-4">
                your playlists will <span className="text-spotify-green font-semibold">automatically update</span> with your latest top tracks when you log in (if they haven't been updated in the last 12 hours). you can have up to three playlists - one for each time range (last month, last 6 months, all time).
              </p>
            </div>

            {isLoading ? (
              <div className="flex justify-center py-12">
                <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-spotify-green"></div>
              </div>
            ) : playlists && playlists.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {playlists.map((playlist: SpotifyPlaylist) => (
                  <div key={playlist.id} className="playlist-card group">
                    <div className="playlist-card-header">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="bg-spotify-green/20 p-2 rounded-full">
                          <ListMusic className="text-spotify-green h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-bold text-lg group-hover:text-spotify-green transition-colors">{playlist.name}</h3>
                          <div className="flex items-center gap-2 text-sm text-spotify-off-white">
                            <span className={`inline-block h-2 w-2 rounded-full ${playlist.time_range === 'short_term' ? 'bg-blue-400' :
                              playlist.time_range === 'medium_term' ? 'bg-purple-400' : 'bg-red-400'
                              }`}></span>
                            {getTimeRangeText(playlist.time_range)} • {playlist.track_count} tracks
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="playlist-card-content">
                      <div className="grid grid-cols-2 gap-2 text-xs text-spotify-off-white">
                        <div className="flex flex-col">
                          <span className="text-[10px] uppercase tracking-wider mb-1">Created</span>
                          <span className="text-white">{formatDate(playlist.created_at).split(' ')[0]}</span>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-[10px] uppercase tracking-wider mb-1">Last Updated</span>
                          <span className="text-white">{formatDate(playlist.last_updated_at).split(' ')[0]}</span>
                        </div>
                      </div>
                    </div>
                    <div className="playlist-card-footer">
                      <a
                        href={playlist.external_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-spotify-green flex items-center gap-1 hover:text-white transition-colors"
                      >
                        <ExternalLink size={16} />
                        <span className="text-sm">open in Spotify</span>
                      </a>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUpdatePlaylist(playlist.id, playlist.time_range)}
                        disabled={updatePlaylistMutation.isPending}
                        className="border-spotify-green text-spotify-green hover:bg-spotify-green hover:text-black transition-all"
                      >
                        {updatePlaylistMutation.isPending && updatePlaylistMutation.variables?.playlistId === playlist.id ? (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="mr-2 h-4 w-4" />
                        )}
                        update
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-spotify-dark-gray rounded-lg p-8 text-center border border-spotify-light-gray/20"
                style={{ backgroundImage: "linear-gradient(to bottom right, rgba(40, 40, 40, 0.3), rgba(20, 20, 20, 0.5))" }}>
                <div className="bg-spotify-green/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                  <ListMusic size={32} className="text-spotify-green" />
                </div>
                <h3 className="text-2xl font-bold mb-3">no playlists yet</h3>
                <p className="text-spotify-off-white mb-6 max-w-md mx-auto">create your first playlist to see your top tracks organized by time period</p>
                <PlaylistCreator buttonText="create your first playlist" className="bg-spotify-green text-black font-bold py-2 px-6 rounded-full hover:bg-spotify-green/90 hover:scale-105 transition-all whitespace-nowrap" />
              </div>
            )}
          </>
        )}
      </div>
    </SpotifyLayout>
  );
};

export default Playlists;
