/**
 * Utility functions for formatting various data types
 */

/**
 * Format duration from milliseconds to MM:SS format
 * @param ms Duration in milliseconds
 * @returns Formatted duration string in MM:SS format
 */
export function formatDuration(ms: number): string {
  if (typeof ms !== 'number' || isNaN(ms)) {
    return '0:00';
  }
  
  try {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('Error formatting duration:', error);
    return '0:00';
  }
}

/**
 * Format a date to a relative time string (e.g., "2 hours ago")
 * @param dateString Date string or Date object
 * @returns Formatted relative time string
 */
export function formatRelativeTime(dateString: string | Date): string {
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 60) {
      return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
    }

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) {
      return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    }

    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) {
      return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    }

    return date.toLocaleDateString();
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return 'Unknown date';
  }
}
