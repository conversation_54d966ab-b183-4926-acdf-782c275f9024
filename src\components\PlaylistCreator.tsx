import React, { useState } from 'react';
import { TimeRange, spotifyService } from '@/services/spotify';
import { useCreatePlaylist, useUpdatePlaylist, useUserPlaylists } from '@/hooks/use-spotify-data';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Music, RefreshCw, ExternalLink } from 'lucide-react';
import TimeRangeSelector from './TimeRangeSelector';
import { toast } from '@/components/ui/sonner';
import { useQueryClient } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogOverlay,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useSpotifyAuth } from '@/contexts/SpotifyAuthContext';

interface PlaylistCreatorProps {
  buttonText?: string;
  className?: string;
}

const PlaylistCreator: React.FC<PlaylistCreatorProps> = ({
  buttonText = 'create playlist',
  className = 'bg-white text-black text-sm font-bold py-1 px-4 rounded-full hover:scale-105 transition-transform whitespace-nowrap'
}) => {
  const [open, setOpen] = useState(false);
  const [timeRange, setTimeRange] = useState<TimeRange>('medium_term');
  const [playlistName, setPlaylistName] = useState('');
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const { user, tokens, loginWithSpotify, logout, loading } = useSpotifyAuth();

  const queryClient = useQueryClient();
  const { data: userPlaylists, isLoading: isLoadingPlaylists, refetch: refetchPlaylists } = useUserPlaylists();
  const createPlaylistMutation = useCreatePlaylist();
  const updatePlaylistMutation = useUpdatePlaylist();

  // Handle button click - either open dialog or prompt login
  const handleButtonClick = () => {
    if (!user || !tokens) {
      toast.error('You need to log in to create playlists');
      loginWithSpotify();
    } else {
      setOpen(true);
    }
  };

  // Find existing playlist for the selected time range
  const existingPlaylist = userPlaylists?.find(playlist => playlist.time_range === timeRange);

  const handleCreateOrUpdatePlaylist = async () => {
    // Clear any previous error details
    setErrorDetails(null);

    try {
      // Check if user is available
      if (!user || !user.id) {
        console.error('User not available:', user);
        toast.error('You must be logged in to create a playlist. Please log in and try again.');
        setErrorDetails('User information is not available. Please log in again.');
        return;
      }

      // Check if tokens are available
      if (!tokens || !tokens.accessToken) {
        console.error('Tokens not available:', tokens);
        toast.error('Authentication token is missing. Please log in again.');
        setErrorDetails('Authentication token is missing. Please log in again.');
        return;
      }

      console.log('Current user:', user);
      console.log('Current tokens available:', !!tokens);
      console.log('Token expires at:', tokens.expiresAt);

      // Check if token is expired
      const now = new Date();
      if (tokens.expiresAt < now) {
        console.error('Token is expired:', tokens.expiresAt);
        toast.error('Your authentication token has expired. Please log in again.');
        setErrorDetails('Authentication token has expired. Please log in again.');
        return;
      }

      if (existingPlaylist) {
        // Update existing playlist
        console.log('Updating existing playlist:', existingPlaylist);
        try {
          await updatePlaylistMutation.mutateAsync({
            playlistId: existingPlaylist.id,
            timeRange
          });
          // Force a refresh of the playlists
          queryClient.invalidateQueries({ queryKey: ['userPlaylists'] });
          await refetchPlaylists();

          toast.success('Playlist updated successfully!');
          setOpen(false);
        } catch (updateError) {
          console.error('Error updating playlist:', updateError);
          const errorMessage = updateError instanceof Error ? updateError.message : 'Unknown error';
          toast.error(`Failed to update playlist: ${errorMessage}`);
          setErrorDetails(`Error updating playlist: ${errorMessage}`);
        }
      } else {
        // Create new playlist
        console.log('Creating new playlist with:', { timeRange, playlistName, userId: user.id });
        try {
          // Validate user ID format before attempting to create playlist
          if (!user.id.match(/^[0-9a-zA-Z]+$/)) {
            throw new Error('Invalid Spotify user ID format. Please log out and log in again.');
          }

          // First check if we can get top tracks for this time range
          try {
            const topTracksCheck = await spotifyService.getTopTracks(timeRange, 1);
            if (!topTracksCheck.items || topTracksCheck.items.length === 0) {
              throw new Error(`No tracks found for the selected time range (${getTimeRangeText(timeRange)}). Try a different time range.`);
            }
            console.log('Top tracks check successful, found tracks for this time range');
          } catch (tracksError) {
            console.error('Error checking top tracks:', tracksError);
            const errorMsg = tracksError instanceof Error ? tracksError.message : 'Unknown error';
            throw new Error(`Unable to fetch your top tracks: ${errorMsg}`);
          }

          // Now create the playlist
          console.log('Attempting to create playlist with:', {
            timeRange,
            playlistName: playlistName || undefined
          });

          try {
            // Set the access token directly before creating the playlist
            spotifyService.setAccessToken(tokens.accessToken);

            // Try a direct call first to test the API
            const testResponse = await fetch(`https://api.spotify.com/v1/me`, {
              headers: {
                'Authorization': `Bearer ${tokens.accessToken}`
              }
            });

            if (!testResponse.ok) {
              const errorText = await testResponse.text();
              console.error(`Test API call failed with status ${testResponse.status}:`, errorText);
              throw new Error(`Spotify API test failed: ${testResponse.status} ${errorText}`);
            } else {
              console.log('Test API call successful - token is valid');
            }

            // Now create the playlist
            const result = await createPlaylistMutation.mutateAsync({
              timeRange,
              playlistName: playlistName || undefined
            });

            console.log('Playlist created successfully:', result);

            // If the playlist was created but has 0 tracks, try to add tracks directly
            if (result && result.id && result.track_count === 0) {
              console.log('Playlist has 0 tracks, attempting to add tracks directly');

              try {
                // Get top tracks
                const topTracks = await spotifyService.getTopTracks(timeRange, 50);

                if (topTracks && topTracks.items && topTracks.items.length > 0) {
                  // Extract track URIs
                  const trackUris = topTracks.items.map(track => track.uri);
                  console.log(`Adding ${trackUris.length} tracks to playlist ${result.id}`);

                  // Add tracks directly
                  await spotifyService.addTracksToPlaylist(result.id, trackUris);
                  console.log('Tracks added successfully');
                }
              } catch (tracksError) {
                console.error('Error adding tracks directly:', tracksError);
                // Continue anyway since the playlist was created
              }
            }

            // Force a refresh of the playlists
            queryClient.invalidateQueries({ queryKey: ['userPlaylists'] });

            // Wait a moment for the database to update
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Refetch the playlists
            await refetchPlaylists();

            // Add the new playlist to the cache directly to ensure it shows up immediately
            if (result && result.id) {
              const currentPlaylists = queryClient.getQueryData<any[]>(['userPlaylists']) || [];

              // Check if the playlist is already in the cache
              const playlistExists = currentPlaylists.some(p => p.id === result.id);

              if (!playlistExists) {
                console.log('Adding new playlist to cache:', result);

                // Add created_at and last_updated_at fields to ensure it's properly displayed
                const enhancedResult = {
                  ...result,
                  created_at: new Date().toISOString(),
                  last_updated_at: new Date().toISOString()
                };

                console.log('Enhanced playlist data for cache:', enhancedResult);

                // Add the new playlist to the cache
                queryClient.setQueryData(['userPlaylists'], [
                  enhancedResult,
                  ...currentPlaylists
                ]);
              }
            }

            // Force another refetch to ensure the playlist is properly loaded
            setTimeout(() => {
              refetchPlaylists();
            }, 2000);

            toast.success('Playlist created successfully!');
            setOpen(false);
          } catch (apiError) {
            console.error('API error creating playlist:', apiError);
            throw apiError; // Re-throw to be caught by the outer catch block
          }
        } catch (createError) {
          console.error('Error creating playlist:', createError);
          const errorMessage = createError instanceof Error ? createError.message : 'Unknown error';
          toast.error(`Failed to create playlist: ${errorMessage}`);

          // Provide more specific error messages based on the error
          if (errorMessage.toLowerCase().includes('token') ||
            errorMessage.toLowerCase().includes('auth') ||
            errorMessage.toLowerCase().includes('expired')) {
            setErrorDetails(`Authentication error: ${errorMessage}\n\nYour Spotify session may have expired. Try logging out and logging in again.`);
          } else if (errorMessage.toLowerCase().includes('permission')) {
            setErrorDetails(`Permission error: ${errorMessage}\n\nThe app doesn't have permission to create playlists. Try logging out and logging in again, making sure to accept all permission requests.`);
          } else if (errorMessage.toLowerCase().includes('rate limit') || errorMessage.toLowerCase().includes('429')) {
            setErrorDetails(`Rate limit exceeded: ${errorMessage}\n\nToo many requests to Spotify API. Please wait a few minutes and try again.`);
          } else if (errorMessage.toLowerCase().includes('network') || errorMessage.toLowerCase().includes('connect')) {
            setErrorDetails(`Network error: ${errorMessage}\n\nCheck your internet connection and try again.`);
          } else {
            setErrorDetails(`Error creating playlist: ${errorMessage}`);
          }
        }
      }
    } catch (error) {
      console.error('Error in handleCreateOrUpdatePlaylist:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to ${existingPlaylist ? 'update' : 'create'} playlist: ${errorMessage}`);
      setErrorDetails(`Error: ${errorMessage}`);
      // Keep the modal open so the user can see the error
    }
  };

  const getTimeRangeText = (range: TimeRange) => {
    return range === 'short_term' ? 'last month' :
      range === 'medium_term' ? 'last 6 months' : 'all time';
  };

  const isLoading = createPlaylistMutation.isPending || updatePlaylistMutation.isPending;

  return (
    <>
      <style>
        {`
          .solid-dialog-overlay {
            background-color: rgba(0, 0, 0, 1) !important;
            backdrop-filter: none !important;
          }
          .solid-dialog-content {
            background-color: #121212 !important;
            backdrop-filter: none !important;
          }
        `}
      </style>
      <button onClick={handleButtonClick} className={className}>
        {loading ? 'Loading...' : buttonText}
      </button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogOverlay className="solid-dialog-overlay" style={{ backgroundColor: 'black', backdropFilter: 'none' }} />
        <DialogContent className="sm:max-w-[425px] text-white border-spotify-light-gray solid-dialog-content" style={{ backgroundColor: '#121212', backdropFilter: 'none' }}>
          <DialogHeader>
            <DialogTitle className="text-xl">create playlist</DialogTitle>
            <DialogDescription className="text-spotify-off-white">
              create a playlist with your top tracks based on a time range
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="mb-4">
              <Label htmlFor="time-range" className="text-white mb-2 block">select time range</Label>
              <TimeRangeSelector selected={timeRange} onChange={setTimeRange} />
            </div>

            {!existingPlaylist && (
              <div className="mb-4">
                <Label htmlFor="playlist-name" className="text-white mb-2 block">playlist name (optional)</Label>
                <Input
                  id="playlist-name"
                  placeholder={`musilize: Top Tracks (${getTimeRangeText(timeRange)})`}
                  value={playlistName}
                  onChange={(e) => setPlaylistName(e.target.value)}
                  className="bg-spotify-light-gray border-spotify-light-gray text-white"
                />
              </div>
            )}

            {existingPlaylist && (
              <div className="playlist-card group mb-4">
                <div className="playlist-card-header">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="bg-spotify-green/20 p-2 rounded-full">
                      <ListMusic className="text-spotify-green h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg group-hover:text-spotify-green transition-colors">{existingPlaylist.name}</h3>
                      <div className="flex items-center gap-2 text-sm text-spotify-off-white">
                        <span className={`inline-block h-2 w-2 rounded-full ${existingPlaylist.time_range === 'short_term' ? 'bg-blue-400' :
                          existingPlaylist.time_range === 'medium_term' ? 'bg-purple-400' : 'bg-red-400'
                          }`}></span>
                        {getTimeRangeText(existingPlaylist.time_range)} • {existingPlaylist.track_count} tracks
                      </div>
                    </div>
                  </div>
                </div>
                <div className="playlist-card-footer">
                  <a
                    href={existingPlaylist.external_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-spotify-green flex items-center gap-1 hover:text-white transition-colors"
                  >
                    <ExternalLink size={16} />
                    <span className="text-sm">open in Spotify</span>
                  </a>
                </div>
              </div>
            )}
          </div>

          {errorDetails && (
            <div className="bg-red-900/30 border border-red-500 rounded-md p-3 mb-4 text-sm text-red-200">
              <h4 className="font-bold mb-1">Error Details</h4>
              <p className="whitespace-pre-line">{errorDetails}</p>

              <div className="mt-3 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={async () => {
                    // Log out and log back in
                    setOpen(false);
                    try {
                      await logout();
                      setTimeout(() => {
                        loginWithSpotify();
                      }, 1000);
                    } catch (error) {
                      console.error('Error during logout:', error);
                      // Just try to log in directly
                      loginWithSpotify();
                    }
                  }}
                  className="border-red-500 text-red-200 hover:bg-red-900/50 text-xs"
                >
                  Log Out & Log In Again
                </Button>
              </div>
            </div>
          )}

          <DialogFooter className="flex-col space-y-2 sm:space-y-0">
            {(!user || !tokens) && (
              <div className="w-full bg-spotify-light-gray rounded-md p-3 mb-2 text-sm text-spotify-off-white">
                <p className="mb-2">You need to be logged in to create playlists</p>
                <Button
                  onClick={loginWithSpotify}
                  className="w-full bg-spotify-green text-black hover:bg-spotify-green/90"
                  disabled={loading}
                >
                  {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Log in with Spotify
                </Button>
              </div>
            )}

            <div className="flex justify-between w-full">
              <Button
                variant="outline"
                onClick={() => setOpen(false)}
                className="border-spotify-light-gray text-white hover:bg-spotify-light-gray"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateOrUpdatePlaylist}
                disabled={isLoading || !user || !tokens}
                className="bg-spotify-green text-black hover:bg-spotify-green/90"
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {existingPlaylist ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Update Playlist
                  </>
                ) : (
                  <>
                    <Music className="mr-2 h-4 w-4" />
                    Create Playlist
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PlaylistCreator;
