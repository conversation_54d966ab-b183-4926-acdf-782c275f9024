// Load environment variables
require('dotenv').config();

// Configuration for different environments
const isDev = process.env.NODE_ENV !== 'production';

// Base URL for the application
const baseUrl = isDev
  ? 'http://localhost:8888'
  : process.env.URL || 'https://musilize.netlify.app'; // Netlify automatically sets the URL env variable

// Ensure we have the required environment variables
if (!process.env.SPOTIFY_CLIENT_ID || !process.env.SPOTIFY_CLIENT_SECRET) {
  console.error('Error: Missing Spotify API credentials. Please set SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET environment variables.');
}

if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
  console.error('Error: Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables.');
}

// Spotify configuration
const spotifyConfig = {
  clientId: process.env.SPOTIFY_CLIENT_ID,
  clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
  redirectUri: process.env.SPOTIFY_REDIRECT_URI || 'https://musilize.netlify.app/.netlify/functions/callback',
};

// Supabase configuration
const supabaseConfig = {
  url: process.env.SUPABASE_URL,
  anonKey: process.env.SUPABASE_ANON_KEY,
};

module.exports = {
  isDev,
  baseUrl,
  spotifyConfig,
  supabaseConfig,
};
